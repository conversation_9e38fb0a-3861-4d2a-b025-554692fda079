package com.miner;

import com.miner.strategy.CalcPerStrategy;
import com.miner.strategy.EMARetraceStrategy;
import com.miner.strategy.ShortTermStrategy;
import com.miner.strategy.TrendFollowingStrategy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class Scheduling {

    @Resource
    CalcPerStrategy calcPerStrategy;

    @Resource
    TrendFollowingStrategy trendFollowingStrategy;

    @Resource
    EMARetraceStrategy emaRetraceStrategy;

    @Resource
    ShortTermStrategy shortTermStrategy;


    @Scheduled(cron = "0 0 1 * * ?")
    public void setLeverage() {
        emaRetraceStrategy.setLever();
    }

    //    @Scheduled(cron = "40 * * * * ?")
//    @Scheduled(cron = "10 14/15 * * * ?")
    public void trendFollowingStrategy() {
        trendFollowingStrategy.run();
    }

    @Scheduled(cron = "40 4/5 * * * ?")
//        @Scheduled(cron = "40 * * * * ?")
    public void emaRetraceStrategy() {
        emaRetraceStrategy.run();
    }

//    @Scheduled(cron = "40 2/3 * * * ?")
//    public void shortTermStrategy() {
//        shortTermStrategy.run();
//    }

}
