package com.miner.strategy;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.miner.system.indicator.KLineEntity;
import com.miner.system.okx.bean.Position;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-05-29
 */
@Component
@Slf4j
public class ShortTermStrategy extends BaseStrategy {
    // 时间周期参数
    private static final String MAIN_TIMEFRAME = "30m";
    private static final String SUB_TIMEFRAME = "5m";
    private static final String LONG_TIMEFRAME = "4h";

    // 技术指标参数
    private static final int ATR_PERIOD = 14;
    private static final int ADX_PERIOD = 14;
    private static final int MACD_FAST = 12;
    private static final int MACD_SLOW = 26;
    private static final int MACD_SIGNAL = 9;

    // ADX阈值参数
    private static final double ADX_TREND_THRESHOLD = 25.0;
    private static final double ADX_STRONG_TREND_THRESHOLD = 40.0;

    // 止损参数
    private static final double STOP_LOSS_ATR_MULTIPLIER = 1.5;
    private static final double STOP_LOSS_MIN_PERCENT = 0.01;

    // 止盈参数
    private static final double TAKE_PROFIT_FIRST_ATR_MULTIPLIER = 1.2;
    private static final double TAKE_PROFIT_SECOND_ATR_MULTIPLIER = 2.0;
    private static final double TAKE_PROFIT_THIRD_ATR_MULTIPLIER = 3.0;

    // 分仓比例参数
    private static final double FIRST_TP_POSITION_PERCENT = 0.4;
    private static final double SECOND_TP_POSITION_PERCENT = 0.3;
    private static final double THIRD_TP_POSITION_PERCENT = 0.3;

    // 移动止损参数
    private static final double TRAILING_STOP_THRESHOLD = 0.8;
    private static final double TRAILING_STOP_STEP_RATIO = 0.2;
    private static final double TRAILING_STOP_LOCK_PROFIT_RATIO = 0.3;

    // 风险控制参数
    private static final double MIN_RISK_REWARD_RATIO = 2.0;
    private static final int MAX_POSITION_PER_DAY = 10;

    // RSI阈值常量
    private static final double RSI_OVERSOLD_THRESHOLD = 25.0;
    private static final double RSI_OVERSOLD_WEAK_THRESHOLD = 40.0;
    private static final double RSI_OVERBOUGHT_THRESHOLD = 75.0;
    private static final double RSI_OVERBOUGHT_WEAK_THRESHOLD = 60.0;
    private static final int RSI_PERIOD = 14;

    // 信号强度常量
    private static final double SIGNAL_STRENGTH_HIGH = 1.0;
    private static final double SIGNAL_STRENGTH_MEDIUM = 0.7;
    private static final double SIGNAL_STRENGTH_LOW = 0.0;

    // 布林带常量
    private static final int BOLLINGER_PERIOD = 20;
    private static final double BOLLINGER_STD_DEV = 2.0;
    private static final double BOLLINGER_CLOSE_FACTOR = 1.005;
    private static final double BOLLINGER_MEDIUM_FACTOR = 1.01;

    // 价格突破常量
    private static final int BREAKOUT_LOOKBACK_PERIOD = 20;
    private static final double VOLUME_BREAKOUT_MULTIPLIER = 1.3;

    // 成交量分析常量
    private static final int VOLUME_ANALYSIS_PERIOD = 10;
    private static final int VOLUME_STATS_PERIOD = 20;
    private static final double VOLUME_PATTERN_CONTINUITY_WEIGHT = 0.3;
    private static final double VOLUME_PATTERN_BASE_WEIGHT = 0.7;

    // 其他常量
    private static final String STRATEGY_KEY_PREFIX = "ShortTermStrategy:";
    private static final int DEFAULT_KLINE_LIMIT = 200;
    private static final int LONG_TERM_KLINE_LIMIT = 100;
    private static final int MIN_DATA_SIZE = 20;

    @Override
    public void run() {
        try {
            // 先检查今日持仓数量
            int currentPositions = countCurrentPositions();
            if (currentPositions >= MAX_POSITION_PER_DAY) {
                log.info("今日持仓数已达上限: {}/{}", currentPositions, MAX_POSITION_PER_DAY);
                return;
            }

            // 获取并管理现有仓位
            manageExistingPositions();

            // 寻找新信号
            findNewEntries(MAX_POSITION_PER_DAY - currentPositions);
        } catch (Exception e) {
            log.error("短线策略运行异常", e);
        }
    }

    /**
     * 获取当前持仓数量
     */
    private int countCurrentPositions() {
        try {
            String keyPattern = STRATEGY_KEY_PREFIX + "*";
            // 这里应使用RedisUtils提供的方法获取匹配的key数量
            // 由于缺少具体实现，用简单逻辑替代
            return 0; // 实际应返回符合模式的key数量
        } catch (Exception e) {
            log.error("获取当前持仓数量异常", e);
            return 0;
        }
    }

    /**
     * 管理现有仓位
     */
    private void manageExistingPositions() {
        List<Position> positions = getPosList();
        if (positions == null || positions.isEmpty()) {
            return;
        }

        for (Position pos : positions) {
            String instId = pos.getInstId();
            String key = STRATEGY_KEY_PREFIX + instId;

            if (!checkTrade(key)) {
                continue;
            }

            try {
                List<KLineEntity> klines = getKline(instId, MAIN_TIMEFRAME, String.valueOf(LONG_TERM_KLINE_LIMIT));
                if (klines.isEmpty()) {
                    log.warn("无法获取K线数据: {}", instId);
                    continue;
                }

                double currentPrice = klines.get(0).getClose().doubleValue();
                boolean isLong = "long".equals(pos.getPosSide());

                updateStopLevels(instId, pos, currentPrice, klines, isLong);
            } catch (Exception e) {
                log.error("管理仓位异常 instId={}", instId, e);
            }
        }
    }

    /**
     * 更新止盈止损
     */
    private void updateStopLevels(String instId, Position pos, double currentPrice,
                                  List<KLineEntity> klines, boolean isLong) {
        // 从Redis获取交易记录
        TradeInfo tradeInfo = getTradeInfo(instId);
        if (tradeInfo == null) return;

        // 计算当前盈亏比例
        double entryPrice = tradeInfo.getEntryPrice();
        double pnlRatio = isLong ?
            (currentPrice / entryPrice - 1) :
            (1 - currentPrice / entryPrice);

        // 更新最大盈利比例
        if (pnlRatio > tradeInfo.getMaxPnlRatio()) {
            tradeInfo.setMaxPnlRatio(pnlRatio);
            updateTradeInfo(instId, tradeInfo);
        }

        // 计算ATR
        double atr = calculateATR(klines, 0, ATR_PERIOD);
        if (atr <= 0) return;
        
        // 计算ADX - 新增
        ADXResult adxResult = calculateADX(klines, ADX_PERIOD);

        // 计算止损移动阈值
        double trailingThreshold = TRAILING_STOP_THRESHOLD * atr / entryPrice;
        
        // 根据ADX调整移动止损阈值 - 新增
        // 趋势越强，移动止损越激进
        double adxFactor = 1.0;
        if (adxResult.hasTrend()) {
            // 如果ADX显示有趋势，根据趋势方向调整移动止损
            boolean adxBullish = adxResult.isBullishTrend();
            
            if ((isLong && adxBullish) || (!isLong && !adxBullish)) {
                // 趋势方向与持仓方向一致，更激进地移动止损
                adxFactor = 1.2 + adxResult.getTrendStrength() * 0.3; // 最多提高50%
                log.info("[{}] ADX显示趋势方向一致({}), 调整移动止损因子为{}", 
                    instId, adxResult.getTrend(), String.format("%.2f", adxFactor));
            } else {
                // 趋势方向与持仓方向相反，更谨慎地移动止损
                adxFactor = 0.8 - adxResult.getTrendStrength() * 0.3; // 最多降低50%
                log.info("[{}] ADX显示趋势方向相反({}), 调整移动止损因子为{}", 
                    instId, adxResult.getTrend(), String.format("%.2f", adxFactor));
            }
        }
        
        // 应用ADX因子到移动止损阈值
        trailingThreshold *= adxFactor;

        // 移动止损逻辑 - 基于当前盈利比例和最大盈利比例
        boolean shouldUpdateStop = false;
        double newStopLevel = tradeInfo.getStopLossPrice();

        // 1. 基础移动止损 - 当盈利超过阈值时
        if (pnlRatio > trailingThreshold) {
            // 计算新的止损价格 - 添加系数下限检查，防止变为负数
            double stopDistanceFactor = Math.max(0.2, 0.8 - tradeInfo.getTrailingStopUpdates() * 0.05);
            
            // 根据ADX调整止损距离 - 新增
            if (adxResult.hasTrend()) {
                boolean adxBullish = adxResult.isBullishTrend();
                if ((isLong && adxBullish) || (!isLong && !adxBullish)) {
                    // 趋势方向一致，缩小止损距离
                    stopDistanceFactor *= (0.8 - adxResult.getTrendStrength() * 0.3);
                } else {
                    // 趋势方向相反，增大止损距离
                    stopDistanceFactor *= (1.2 + adxResult.getTrendStrength() * 0.3);
                }
            }
            
            double basicStopLevel = isLong ?
                Math.max(entryPrice * 1.002, currentPrice - atr * stopDistanceFactor) :
                Math.min(entryPrice * 0.998, currentPrice + atr * stopDistanceFactor);

            // 2. 利润回撤保护 - 当从最高点回撤超过一定比例时，提前移动止损
            double drawdown = tradeInfo.getMaxPnlRatio() - pnlRatio;
            
            // 根据ADX调整回撤阈值 - 新增
            double drawdownThreshold = tradeInfo.getMaxPnlRatio() * TRAILING_STOP_LOCK_PROFIT_RATIO;
            if (adxResult.hasTrend()) {
                boolean adxBullish = adxResult.isBullishTrend();
                if ((isLong && !adxBullish) || (!isLong && adxBullish)) {
                    // 趋势方向与持仓方向相反，降低回撤阈值，更早锁定利润
                    drawdownThreshold *= (0.7 - adxResult.getTrendStrength() * 0.2);
                    log.info("[{}] ADX显示反向趋势，降低回撤阈值为{}", 
                        instId, String.format("%.2f%%", drawdownThreshold * 100));
                }
            }

            if (drawdown > drawdownThreshold && tradeInfo.getMaxPnlRatio() > trailingThreshold * 2) {
                // 回撤保护止损 - 更激进地移动止损以锁定利润
                double protectiveStopLevel = isLong ?
                    Math.max(basicStopLevel, currentPrice - atr * 0.5) :
                    Math.min(basicStopLevel, currentPrice + atr * 0.5);

                newStopLevel = isLong ?
                    Math.max(newStopLevel, protectiveStopLevel) :
                    Math.min(newStopLevel, protectiveStopLevel);

                shouldUpdateStop = true;
                log.info("[{}] 触发回撤保护止损: 最大盈利={}, 当前盈利={}, 回撤={}",
                    instId, String.format("%.2f%%", tradeInfo.getMaxPnlRatio() * 100),
                    String.format("%.2f%%", pnlRatio * 100),
                    String.format("%.2f%%", drawdown * 100));
            }
            // 3. 基础移动止损更新
            else if ((isLong && basicStopLevel > newStopLevel) || (!isLong && basicStopLevel < newStopLevel)) {
                newStopLevel = basicStopLevel;
                shouldUpdateStop = true;
            }
        }

        // 执行止损更新
        if (shouldUpdateStop) {
            // 检查是否优于当前止损
            double currentStop = tradeInfo.getStopLossPrice();
            if ((isLong && newStopLevel > currentStop) || (!isLong && newStopLevel < currentStop)) {
                // 先取消现有的止损订单
                cancelAlgoOrders(instId);

                // 设置新的止损
                String side = isLong ? "sell" : "buy";
                String stopPx = getPz(newStopLevel, klines.get(0).getClose().scale());
                algoTradeLoss(instId, side, pos.getAvailPos(), pos.getPosSide(), stopPx);

                // 根据已执行的止盈级别，重设止盈单
                if (tradeInfo.isSecondTpExecuted()) {
                    // 如果第二级止盈已执行，只需重设第三级止盈单
                    String thirdTpPx = String.valueOf(tradeInfo.getThirdTakeProfitPrice());
                    algoTradeWin(instId, side, pos.getAvailPos(), pos.getPosSide(), thirdTpPx);
                } else if (tradeInfo.isFirstTpExecuted()) {
                    // 如果第一级止盈已执行，需要重设第二级和第三级止盈单
                    String secondTpPx = String.valueOf(tradeInfo.getSecondTakeProfitPrice());
                    String thirdTpPx = String.valueOf(tradeInfo.getThirdTakeProfitPrice());

                    // 计算剩余仓位分配
                    BigDecimal totalPos = new BigDecimal(pos.getAvailPos());
                    // 简化计算：第二级止盈占剩余仓位的比例
                    double remainingRatio = SECOND_TP_POSITION_PERCENT / (1 - FIRST_TP_POSITION_PERCENT);
                    BigDecimal secondTpPos = totalPos.multiply(new BigDecimal(remainingRatio))
                        .setScale(new BigDecimal(pos.getAvailPos()).scale(), RoundingMode.DOWN);
                    String secondTpSz = secondTpPos.toString();

                    // 第三级止盈的仓位
                    BigDecimal thirdTpPos = totalPos.subtract(secondTpPos);
                    String thirdTpSz = thirdTpPos.toString();

                    // 设置止盈单
                    algoTradeWin(instId, side, secondTpSz, pos.getPosSide(), secondTpPx);
                    algoTradeWin(instId, side, thirdTpSz, pos.getPosSide(), thirdTpPx);
                } else {
                    // 如果所有止盈都未执行，需要重设三级止盈单
                    String firstTpPx = String.valueOf(tradeInfo.getFirstTakeProfitPrice());
                    String secondTpPx = String.valueOf(tradeInfo.getSecondTakeProfitPrice());
                    String thirdTpPx = String.valueOf(tradeInfo.getThirdTakeProfitPrice());

                    // 计算分仓位大小
                    BigDecimal totalPos = new BigDecimal(pos.getAvailPos());
                    BigDecimal firstTpPos = totalPos.multiply(new BigDecimal(FIRST_TP_POSITION_PERCENT))
                        .setScale(new BigDecimal(pos.getAvailPos()).scale(), RoundingMode.DOWN);
                    String firstTpSz = firstTpPos.toString();

                    BigDecimal secondTpPos = totalPos.multiply(new BigDecimal(SECOND_TP_POSITION_PERCENT))
                        .setScale(new BigDecimal(pos.getAvailPos()).scale(), RoundingMode.DOWN);
                    String secondTpSz = secondTpPos.toString();

                    BigDecimal thirdTpPos = totalPos.subtract(firstTpPos).subtract(secondTpPos);
                    String thirdTpSz = thirdTpPos.toString();

                    // 设置三级止盈单
                    algoTradeWin(instId, side, firstTpSz, pos.getPosSide(), firstTpPx);
                    algoTradeWin(instId, side, secondTpSz, pos.getPosSide(), secondTpPx);
                    algoTradeWin(instId, side, thirdTpSz, pos.getPosSide(), thirdTpPx);
                }

                // 更新Redis记录
                tradeInfo.setStopLossPrice(newStopLevel);
                tradeInfo.setTrailingStopUpdates(tradeInfo.getTrailingStopUpdates() + 1);
                tradeInfo.setLastUpdateTime(System.currentTimeMillis());
                updateTradeInfo(instId, tradeInfo);

                // 通知
                String message = MessageFormat.format("{0}移动止损: {1} -> {2} (盈利: {3}%, 最大: {4}%, ADX: {5})",
                    instId,
                    String.format("%.4f", currentStop),
                    String.format("%.4f", newStopLevel),
                    String.format("%.2f", pnlRatio * 100),
                    String.format("%.2f", tradeInfo.getMaxPnlRatio() * 100),
                    String.format("%.1f", adxResult.getAdx())); // 添加ADX信息
                messageService.send("短线移动止损", message);
            }
        }

        // 检查止盈触发情况
        checkTakeProfitExecution(instId, tradeInfo, currentPrice, isLong);
    }

    /**
     * 从Redis获取交易记录
     */
    private TradeInfo getTradeInfo(String instId) {
        String key = STRATEGY_KEY_PREFIX + instId;
        return com.miner.common.utils.redis.RedisUtils.getCacheObject(key);
    }

    /**
     * 更新交易记录到Redis
     */
    private void updateTradeInfo(String instId, TradeInfo tradeInfo) {
        String key = STRATEGY_KEY_PREFIX + instId;
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, tradeInfo, Duration.ofHours(4));
    }

    /**
     * 寻找新的入场机会
     */
    private void findNewEntries(int maxNewPositions) {
        if (maxNewPositions <= 0) {
            return;
        }

        List<String> instIds = getInstIds(50);
        for (String instId : instIds) {
            if (checkTrade(STRATEGY_KEY_PREFIX + instId)) {
                continue;
            }

            KLineData klineData = getMultiTimeframeKlines(instId);
            if (!klineData.isValid()) {
                continue;
            }

            // 检查做多信号
            SignalResult longSignal = confirmEntrySignal(klineData, true);
            if (longSignal.isValid) {
                BigDecimal currentPrice = klineData.getShortKlines().get(0).getClose();
                processEntry(instId, currentPrice, klineData.getMainKlines(), true, longSignal.strength);
                continue;
            }

            // 检查做空信号
            SignalResult shortSignal = confirmEntrySignal(klineData, false);
            if (shortSignal.isValid) {
                BigDecimal currentPrice = klineData.getShortKlines().get(0).getClose();
                processEntry(instId, currentPrice, klineData.getMainKlines(), false, shortSignal.strength);
            }
        }
    }

    /**
     * 获取多时间周期K线数据
     */
    private KLineData getMultiTimeframeKlines(String instId) {
        List<KLineEntity> mainKlines = getKline(instId, MAIN_TIMEFRAME, String.valueOf(DEFAULT_KLINE_LIMIT));
        List<KLineEntity> shortKlines = getKline(instId, SUB_TIMEFRAME, String.valueOf(DEFAULT_KLINE_LIMIT));
        List<KLineEntity> longKlines = getKline(instId, LONG_TIMEFRAME, String.valueOf(LONG_TERM_KLINE_LIMIT));

        return new KLineData(mainKlines, shortKlines, longKlines);
    }

    /**
     * 信号确认逻辑，增强版
     */
    private SignalResult confirmEntrySignal(KLineData klineData, boolean isLong) {
        if (!klineData.hasMinimumData(20)) {
            return new SignalResult(false, 0);
        }

        List<KLineEntity> klineShort = klineData.getShortKlines();
        List<KLineEntity> klineLong = klineData.getMainKlines();
        List<KLineEntity> kline4h = klineData.getLongKlines();

        // 1. 短周期技术指标分析
        double rsiStrength = calculateRsiStrength(klineShort, isLong);
        double macdStrength = calculateMacdStrength(klineShort, isLong);
        double bollingerStrength = calculateBollingerStrength(klineShort, isLong);

        // 2. 短周期价格形态分析
        double patternStrength = calculatePatternStrength(klineShort, isLong);
        double priceBreakoutStrength = calculatePriceBreakoutStrength(klineShort, isLong);

        // 3. 波动特征分析
        VolatilityAnalysisResult volatilityResult = analyzeVolatilityFeatures(klineShort, klineLong, kline4h, isLong);

        // 4. 增强的成交量分析
        VolumeAnalysisResult volumeResult = analyzeVolumeFeatures(klineShort, klineLong, kline4h, isLong);

        // 5. 长周期趋势分析
        double trendStrength = calculateTrendStrength(klineLong, kline4h, isLong);
        double supportResistanceStrength = calculateSupportResistanceStrength(klineLong, klineShort.get(0).getClose().doubleValue(), isLong);
        double longTermVolatility = calculateVolatility(klineLong);

        // 6. 长短周期协同分析
        double crossTimeframeStrength = calculateCrossTimeframeStrength(klineShort, klineLong, kline4h, isLong);
        
        // 7. ADX趋势强度分析 - 新增
        ADXTrendResult adxResult = analyzeADXTrend(klineShort, klineLong, kline4h, isLong);

        // 8. 计算各分类的综合得分
        double technicalScore = calculateTechnicalScore(macdStrength, rsiStrength, bollingerStrength);
        double patternScore = calculatePatternScore(patternStrength, priceBreakoutStrength);
        double volatilityScore = calculateVolatilityScore(volatilityResult);
        double volumeScore = calculateEnhancedVolumeScore(volumeResult);
        double trendScore = calculateTrendScore(trendStrength, supportResistanceStrength);
        double crossTimeframeScore = crossTimeframeStrength;
        double adxScore = adxResult.getAdxScore(); // 新增ADX得分

        // 9. 计算信号一致性评分 (更新)
        SignalConsistencyResult consistencyResult = calculateSignalConsistency(
            technicalScore, patternScore, volatilityScore, volumeScore, 
            trendScore, crossTimeframeScore, adxScore, isLong); // 添加ADX得分
        double consistencyBonus = consistencyResult.getConsistencyScore();

        // 10. 动态调整阈值 (提高基础阈值，增加波动率影响)
        double volatility = (calculateVolatility(klineShort) + longTermVolatility) / 2;
        double baseThreshold = 0.65; // 从0.5提高到0.65，提高触发阈值
        double dynamicThreshold = baseThreshold * (1 + (volatility - 1) * 0.3); // 增加波动率影响从0.2提高到0.3

        // 11. 计算最终得分（减少一致性奖励的影响）
        double totalScore = (technicalScore * 0.20) +
            (patternScore * 0.15) +
            (volatilityScore * 0.15) +
            (volumeScore * 0.15) +
            (trendScore * 0.15) +
            (crossTimeframeScore * 0.10) +
            (adxScore * 0.10); // 新增ADX权重

        // 添加一致性奖励（最多增加10%的得分，从15%降低到10%）
        double finalScore = totalScore * (1.0 + consistencyBonus * 0.10);

        // 12. 记录详细日志
        log.info("[{}] 信号分析 - 技术={}, 形态={}, 波动={}, 成交量={}, 趋势={}, 协同={}, ADX={}, 一致性={}%, 最终={}/阈值={}",
            isLong ? "多头" : "空头",
            String.format("%.2f", technicalScore),
            String.format("%.2f", patternScore),
            String.format("%.2f", volatilityScore),
            String.format("%.2f", volumeScore),
            String.format("%.2f", trendScore),
            String.format("%.2f", crossTimeframeScore),
            String.format("%.2f", adxScore), // 新增ADX日志
            String.format("%.0f", consistencyBonus * 100),
            String.format("%.2f", finalScore),
            String.format("%.2f", dynamicThreshold));

        // 13. 记录波动、成交量、ADX和一致性详细分析
        log.info("[{}] 波动分析 - {}", isLong ? "多头" : "空头", volatilityResult.getLogInfo());
        log.info("[{}] 成交量分析 - {}", isLong ? "多头" : "空头", volumeResult.getLogInfo());
        log.info("[{}] ADX分析 - {}", isLong ? "多头" : "空头", adxResult.getLogInfo()); // 新增ADX日志
        log.info("[{}] 一致性分析 - {}", isLong ? "多头" : "空头", consistencyResult.getLogInfo());

        // 14. ADX趋势过滤 - 如果ADX显示强趋势但方向与交易方向相反，拒绝信号
        if (adxResult.isStrongCounterTrend()) {
            log.info("[{}] 信号被ADX趋势过滤 - 存在强反向趋势", isLong ? "多头" : "空头");
            return new SignalResult(false, finalScore);
        }

        return new SignalResult(finalScore > dynamicThreshold, finalScore);
    }
    
    /**
     * ADX趋势分析结果
     */
    @Data
    private static class ADXTrendResult {
        private final double adxScore;           // ADX综合得分
        private final boolean isStrongCounterTrend; // 是否存在强反向趋势
        private final String logInfo;            // 详细日志信息
        
        public ADXTrendResult(double adxScore, boolean isStrongCounterTrend, String logInfo) {
            this.adxScore = adxScore;
            this.isStrongCounterTrend = isStrongCounterTrend;
            this.logInfo = logInfo;
        }
    }
    
    /**
     * 分析多周期ADX趋势
     */
    private ADXTrendResult analyzeADXTrend(List<KLineEntity> klineShort, 
                                          List<KLineEntity> klineLong, 
                                          List<KLineEntity> kline4h, 
                                          boolean isLong) {
        // 计算三个周期的ADX
        ADXResult shortAdx = calculateADX(klineShort, ADX_PERIOD);
        ADXResult longAdx = calculateADX(klineLong, ADX_PERIOD);
        ADXResult longTermAdx = calculateADX(kline4h, ADX_PERIOD);
        
        // 判断各周期趋势方向是否与交易方向一致
        boolean shortTermAligned = isLong == shortAdx.isBullishTrend();
        boolean longTermAligned = isLong == longAdx.isBullishTrend();
        boolean longTermTrendAligned = isLong == longTermAdx.isBullishTrend();
        
        // 检查是否存在强反向趋势
        boolean hasStrongCounterTrend = false;
        
        // 如果长周期存在强趋势且方向与交易方向相反，这是强反向趋势信号
        if (longTermAdx.hasStrongTrend() && !longTermTrendAligned) {
            hasStrongCounterTrend = true;
        }
        
        // 如果中周期存在强趋势且方向与交易方向相反，这也是强反向趋势信号
        if (longAdx.hasStrongTrend() && !longTermAligned) {
            hasStrongCounterTrend = true;
        }
        
        // 计算趋势强度得分
        double shortTermScore = shortAdx.getTrendStrength() * (shortTermAligned ? 1 : -0.5);
        double longTermScore = longAdx.getTrendStrength() * (longTermAligned ? 1 : -0.5);
        double longTermTrendScore = longTermAdx.getTrendStrength() * (longTermTrendAligned ? 1 : -0.5);
        
        // 综合得分 (短期30%，中期30%，长期40%)
        double totalScore = (shortTermScore * 0.3 + longTermScore * 0.3 + longTermTrendScore * 0.4);
        
        // 归一化到0-1范围
        double normalizedScore = (totalScore + 0.5) / 1.5;
        normalizedScore = Math.max(0, Math.min(normalizedScore, 1.0));
        
        // 生成日志信息
        String logInfo = String.format(
            "短期ADX=%.1f(%s), 中期ADX=%.1f(%s), 长期ADX=%.1f(%s), 短期方向=%s, 中期方向=%s, 长期方向=%s, 得分=%.2f",
            shortAdx.getAdx(), shortAdx.getTrend(),
            longAdx.getAdx(), longAdx.getTrend(),
            longTermAdx.getAdx(), longTermAdx.getTrend(),
            shortTermAligned ? "一致" : "反向",
            longTermAligned ? "一致" : "反向",
            longTermTrendAligned ? "一致" : "反向",
            normalizedScore
        );
        
        return new ADXTrendResult(normalizedScore, hasStrongCounterTrend, logInfo);
    }

    /**
     * 信号一致性评分结果
     */
    @Data
    private static class SignalConsistencyResult {
        private final double consistencyScore;  // 一致性得分（0-1）
        private final String logInfo;           // 详细日志信息

        public SignalConsistencyResult(double consistencyScore, String logInfo) {
            this.consistencyScore = consistencyScore;
            this.logInfo = logInfo;
        }
    }

    /**
     * 计算信号一致性评分
     * 分析各类指标之间的一致性程度，当多个指标同时给出相同方向的信号时，提高总体评分
     */
    private SignalConsistencyResult calculateSignalConsistency(
        double technicalScore, double patternScore, double volatilityScore,
        double volumeScore, double trendScore, double crossTimeframeScore, double adxScore, boolean isLong) {

        // 1. 设置强信号阈值（超过此值视为强信号）
        final double STRONG_SIGNAL_THRESHOLD = 0.75; // 从0.65提高到0.75，提高强信号标准

        // 2. 计算各类别是否为强信号
        boolean technicalStrong = technicalScore >= STRONG_SIGNAL_THRESHOLD;
        boolean patternStrong = patternScore >= STRONG_SIGNAL_THRESHOLD;
        boolean volatilityStrong = volatilityScore >= STRONG_SIGNAL_THRESHOLD;
        boolean volumeStrong = volumeScore >= STRONG_SIGNAL_THRESHOLD;
        boolean trendStrong = trendScore >= STRONG_SIGNAL_THRESHOLD;
        boolean crossTimeframeStrong = crossTimeframeScore >= STRONG_SIGNAL_THRESHOLD;
        boolean adxStrong = adxScore >= STRONG_SIGNAL_THRESHOLD;

        // 3. 统计强信号数量
        int strongSignalCount = 0;
        if (technicalStrong) strongSignalCount++;
        if (patternStrong) strongSignalCount++;
        if (volatilityStrong) strongSignalCount++;
        if (volumeStrong) strongSignalCount++;
        if (trendStrong) strongSignalCount++;
        if (crossTimeframeStrong) strongSignalCount++;
        if (adxStrong) strongSignalCount++;

        // 4. 计算一致性得分
        // 基础分：强信号数量 / 总类别数
        double baseConsistencyScore = (double) strongSignalCount / 7.0; // 从6.0改为7.0

        // 5. 分析关键组合
        double combinationBonus = 0.0;
        StringBuilder combinations = new StringBuilder();

        // 技术指标 + 成交量（经典组合）
        if (technicalStrong && volumeStrong) {
            combinationBonus += 0.2;
            combinations.append("技术+成交量 ");
        }

        // 价格形态 + 支撑阻力（形态确认）
        if (patternStrong && trendStrong) {
            combinationBonus += 0.15;
            combinations.append("形态+趋势 ");
        }

        // 波动 + 成交量（突破确认）
        if (volatilityStrong && volumeStrong) {
            combinationBonus += 0.15;
            combinations.append("波动+成交量 ");
        }

        // 跨周期 + 趋势（多周期确认）
        if (crossTimeframeStrong && trendStrong) {
            combinationBonus += 0.15;
            combinations.append("跨周期+趋势 ");
        }

        // 技术 + 价格形态 + 成交量（三重确认）
        if (technicalStrong && patternStrong && volumeStrong) {
            combinationBonus += 0.25;
            combinations.append("技术+形态+成交量 ");
        }
        
        // 新增: ADX + 趋势（趋势确认）
        if (adxStrong && trendStrong) {
            combinationBonus += 0.2;
            combinations.append("ADX+趋势 ");
        }
        
        // 新增: ADX + 技术指标（技术确认）
        if (adxStrong && technicalStrong) {
            combinationBonus += 0.15;
            combinations.append("ADX+技术 ");
        }
        
        // 新增: ADX + 跨周期（多周期趋势一致性）
        if (adxStrong && crossTimeframeStrong) {
            combinationBonus += 0.15;
            combinations.append("ADX+跨周期 ");
        }
        
        // 新增: 趋势 + 技术 + ADX（强趋势确认）
        if (trendStrong && technicalStrong && adxStrong) {
            combinationBonus += 0.3;
            combinations.append("趋势+技术+ADX ");
        }

        // 6. 计算最终一致性得分（基础分 + 组合奖励，上限为1.0）
        double finalConsistencyScore = Math.min(baseConsistencyScore + combinationBonus, 1.0);

        // 7. 生成日志信息
        String logInfo = String.format(
            "强信号=%d/7 (技术=%s, 形态=%s, 波动=%s, 成交量=%s, 趋势=%s, 跨周期=%s, ADX=%s), 组合奖励=%.2f, 组合=[%s]",
            strongSignalCount,
            technicalStrong ? "强" : "弱",
            patternStrong ? "强" : "弱",
            volatilityStrong ? "强" : "弱",
            volumeStrong ? "强" : "弱",
            trendStrong ? "强" : "弱",
            crossTimeframeStrong ? "强" : "弱",
            adxStrong ? "强" : "弱",
            combinationBonus,
            combinations.length() > 0 ? combinations.toString() : "无"
        );

        return new SignalConsistencyResult(finalConsistencyScore, logInfo);
    }

    /**
     * 波动特征分析结果
     */
    @Data
    private static class VolatilityAnalysisResult {
        private final double atrBreakoutStrength;    // ATR通道突破强度
        private final double volatilityCycleStrength; // 波动周期特征强度
        private final double priceStructureStrength;  // 价格结构特征强度
        private final String logInfo;                 // 详细日志信息

        public VolatilityAnalysisResult(double atrBreakoutStrength, double volatilityCycleStrength,
                                        double priceStructureStrength, String logInfo) {
            this.atrBreakoutStrength = atrBreakoutStrength;
            this.volatilityCycleStrength = volatilityCycleStrength;
            this.priceStructureStrength = priceStructureStrength;
            this.logInfo = logInfo;
        }
    }

    /**
     * 增强的成交量分析结果
     */
    @Data
    private static class VolumeAnalysisResult {
        private final double volumeBreakoutStrength;  // 成交量突破强度
        private final double buyPressureStrength;     // 买压强度
        private final double volumePatternStrength;   // 成交量形态强度
        private final double vwapTrendStrength;       // VWAP趋势强度
        private final String logInfo;                 // 详细日志信息

        public VolumeAnalysisResult(double volumeBreakoutStrength, double buyPressureStrength,
                                    double volumePatternStrength, double vwapTrendStrength, String logInfo) {
            this.volumeBreakoutStrength = volumeBreakoutStrength;
            this.buyPressureStrength = buyPressureStrength;
            this.volumePatternStrength = volumePatternStrength;
            this.vwapTrendStrength = vwapTrendStrength;
            this.logInfo = logInfo;
        }
    }

    /**
     * 分析波动特征
     */
    private VolatilityAnalysisResult analyzeVolatilityFeatures(List<KLineEntity> klineShort,
                                                               List<KLineEntity> klineLong,
                                                               List<KLineEntity> kline4h,
                                                               boolean isLong) {
        // 1. ATR通道分析 - 短周期(5分钟)
        double atrShort = calculateATR(klineShort, 0, ATR_PERIOD);
        double currentPrice = klineShort.get(0).getClose().doubleValue();
        double prevPrice = klineShort.get(1).getClose().doubleValue();

        // 计算ATR通道
        double upperBandShort = prevPrice + atrShort * 2;
        double lowerBandShort = prevPrice - atrShort * 2;

        // 判断是否突破ATR通道
        double atrBreakoutStrength = 0.0;
        if (isLong && currentPrice > upperBandShort) {
            atrBreakoutStrength = Math.min((currentPrice - upperBandShort) / atrShort, 1.0);
        } else if (!isLong && currentPrice < lowerBandShort) {
            atrBreakoutStrength = Math.min((lowerBandShort - currentPrice) / atrShort, 1.0);
        }

        // 2. 中周期(30分钟)波动率分析
        double atrLong = calculateATR(klineLong, 0, ATR_PERIOD);
        double volatilityRatio = atrShort / (klineShort.get(0).getClose().doubleValue()) / 
                                (atrLong / (klineLong.get(0).getClose().doubleValue()));
                                
        // 波动率比值大于1.2表示短周期波动率明显高于中周期，可能是突破信号
        double volatilityRatioStrength = Math.min(volatilityRatio / 1.2, 1.0);
        
        // 3. 长周期(4小时)波动周期分析
        double volatilityCycleStrength = analyzeVolatilityCycle(kline4h);

        // 4. 价格结构分析 - 结合多个周期
        double priceStructureStrength = analyzePriceStructure(klineShort, klineLong, kline4h, isLong);

        // 5. 波动收缩分析 - 检测波动率收缩后的爆发
        double volatilityContractionStrength = analyzeVolatilityContraction(klineShort, klineLong);

        // 生成日志信息
        String logInfo = String.format(
            "ATR突破=%.2f, 波动比=%.2f, 周期特征=%.2f, 结构特征=%.2f, 波动收缩=%.2f, ATR=%.2f, 通道[%.2f-%.2f]",
            atrBreakoutStrength, volatilityRatioStrength, volatilityCycleStrength, 
            priceStructureStrength, volatilityContractionStrength,
            atrShort, lowerBandShort, upperBandShort
        );

        return new VolatilityAnalysisResult(
            atrBreakoutStrength,
            volatilityCycleStrength,
            priceStructureStrength,
            logInfo
        );
    }
    
    /**
     * 分析价格结构特征 - 结合多个周期
     */
    private double analyzePriceStructure(List<KLineEntity> klineShort, List<KLineEntity> klineLong, 
                                         List<KLineEntity> kline4h, boolean isLong) {
        if (klineShort.size() < 20 || klineLong.size() < 20 || kline4h.size() < 10) return 0.0;

        // 1. 短周期(5分钟)高低点分析
        List<Double> highsShort = new ArrayList<>();
        List<Double> lowsShort = new ArrayList<>();
        findSwingPoints(klineShort, highsShort, lowsShort);

        // 2. 中周期(30分钟)高低点分析
        List<Double> highsLong = new ArrayList<>();
        List<Double> lowsLong = new ArrayList<>();
        findSwingPoints(klineLong, highsLong, lowsLong);
        
        // 3. 长周期(4小时)高低点分析
        List<Double> highsLongTerm = new ArrayList<>();
        List<Double> lowsLongTerm = new ArrayList<>();
        findSwingPoints(kline4h, highsLongTerm, lowsLongTerm);

        // 分析短周期高低点序列的趋势
        boolean shortHigherHighs = highsShort.size() >= 2 && highsShort.get(0) > highsShort.get(highsShort.size() - 1);
        boolean shortHigherLows = lowsShort.size() >= 2 && lowsShort.get(0) > lowsShort.get(lowsShort.size() - 1);
        
        // 分析中周期高低点序列的趋势
        boolean longHigherHighs = highsLong.size() >= 2 && highsLong.get(0) > highsLong.get(highsLong.size() - 1);
        boolean longHigherLows = lowsLong.size() >= 2 && lowsLong.get(0) > lowsLong.get(lowsLong.size() - 1);
        
        // 分析长周期高低点序列的趋势
        boolean longTermHigherHighs = highsLongTerm.size() >= 2 && 
                                    highsLongTerm.get(0) > highsLongTerm.get(highsLongTerm.size() - 1);
        boolean longTermHigherLows = lowsLongTerm.size() >= 2 && 
                                   lowsLongTerm.get(0) > lowsLongTerm.get(lowsLongTerm.size() - 1);

        // 计算多周期结构得分
        double shortScore = 0.0;
        double longScore = 0.0;
        double longTermScore = 0.0;
        
        if (isLong) {
            // 多头结构：更高的低点
            shortScore = shortHigherLows ? 1.0 : 0.0;
            longScore = longHigherLows ? 1.0 : 0.0;
            longTermScore = longTermHigherLows ? 1.0 : 0.0;
        } else {
            // 空头结构：更低的高点
            shortScore = !shortHigherHighs ? 1.0 : 0.0;
            longScore = !longHigherHighs ? 1.0 : 0.0;
            longTermScore = !longTermHigherHighs ? 1.0 : 0.0;
        }
        
        // 加权计算总分 (短期30%，中期30%，长期40%)
        return shortScore * 0.3 + longScore * 0.3 + longTermScore * 0.4;
    }
    
    /**
     * 查找K线中的高低点
     */
    private void findSwingPoints(List<KLineEntity> klines, List<Double> highs, List<Double> lows) {
        for (int i = 1; i < klines.size() - 1; i++) {
            KLineEntity prev = klines.get(i - 1);
            KLineEntity curr = klines.get(i);
            KLineEntity next = klines.get(i + 1);

            // 高点判断
            if (curr.getHigh().doubleValue() > prev.getHigh().doubleValue() &&
                curr.getHigh().doubleValue() > next.getHigh().doubleValue()) {
                highs.add(curr.getHigh().doubleValue());
            }

            // 低点判断
            if (curr.getLow().doubleValue() < prev.getLow().doubleValue() &&
                curr.getLow().doubleValue() < next.getLow().doubleValue()) {
                lows.add(curr.getLow().doubleValue());
            }
        }
    }
    
    /**
     * 分析波动收缩
     * 检测波动率收缩后的爆发，这通常是趋势变化的信号
     */
    private double analyzeVolatilityContraction(List<KLineEntity> klineShort, List<KLineEntity> klineLong) {
        if (klineShort.size() < 20 || klineLong.size() < 20) return 0.0;
        
        // 计算最近20根K线的波动率
        List<Double> volatilities = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            double range = (klineShort.get(i).getHigh().doubleValue() - klineShort.get(i).getLow().doubleValue()) /
                klineShort.get(i).getClose().doubleValue() * 100; // 转为百分比
            volatilities.add(range);
        }
        
        // 计算最近5根和之前15根的平均波动率
        double recentAvg = volatilities.subList(0, 5).stream().mapToDouble(Double::doubleValue).average().orElse(0);
        double previousAvg = volatilities.subList(5, 20).stream().mapToDouble(Double::doubleValue).average().orElse(0);
        
        // 如果最近的波动率明显高于之前的波动率，表示可能是波动收缩后的爆发
        if (recentAvg > previousAvg * 1.5) {
            return Math.min(recentAvg / previousAvg / 2, 1.0);
        }
        
        // 如果最近的波动率明显低于之前的波动率，表示可能是波动收缩阶段
        if (recentAvg < previousAvg * 0.7) {
            return 0.3; // 波动收缩阶段，给予较低分数
        }
        
        return 0.0;
    }

    /**
     * 计算波动分析得分
     */
    private double calculateVolatilityScore(VolatilityAnalysisResult result) {
        // 提高ATR突破权重，降低周期特征权重
        return result.getAtrBreakoutStrength() * 0.5 +
            result.getVolatilityCycleStrength() * 0.2 +
            result.getPriceStructureStrength() * 0.3;
    }

    /**
     * 计算增强的成交量得分
     */
    private double calculateEnhancedVolumeScore(VolumeAnalysisResult result) {
        // 提高成交量突破和买压权重，降低VWAP趋势权重
        return result.getVolumeBreakoutStrength() * 0.4 +
            result.getBuyPressureStrength() * 0.4 +
            result.getVolumePatternStrength() * 0.15 +
            result.getVwapTrendStrength() * 0.05;
    }

    /**
     * 提取K线收盘价列表
     */
    private List<Double> extractClosePrices(List<KLineEntity> klines) {
        return klines.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());
    }

    /**
     * 计算RSI强度
     */
    private double calculateRsiStrength(List<KLineEntity> klines, boolean isLong) {
        List<Double> prices = extractClosePrices(klines);
        double rsi = calculateRSI(prices, 0, RSI_PERIOD);

        if (isLong) {
            if (rsi <= RSI_OVERSOLD_THRESHOLD) {
                return SIGNAL_STRENGTH_HIGH;
            }
            if (rsi <= RSI_OVERSOLD_WEAK_THRESHOLD) {
                return SIGNAL_STRENGTH_MEDIUM;
            }
            return SIGNAL_STRENGTH_LOW;
        } else {
            if (rsi >= RSI_OVERBOUGHT_THRESHOLD) {
                return SIGNAL_STRENGTH_HIGH;
            }
            if (rsi >= RSI_OVERBOUGHT_WEAK_THRESHOLD) {
                return SIGNAL_STRENGTH_MEDIUM;
            }
            return SIGNAL_STRENGTH_LOW;
        }
    }

    /**
     * 计算MACD强度
     */
    private double calculateMacdStrength(List<KLineEntity> klines, boolean isLong) {
        List<Double> prices = extractClosePrices(klines);

        double[] macd = calculateMACD(prices);
        double[] prevMacd = calculateMACD(prices.subList(1, prices.size()));
        double[] prev2Macd = calculateMACD(prices.subList(2, prices.size()));

        // MACD交叉信号
        boolean crossSignal = isLong ?
            (macd[0] > macd[1] && prevMacd[0] <= prevMacd[1]) :  // 金叉
            (macd[0] < macd[1] && prevMacd[0] >= prevMacd[1]);   // 死叉

        // MACD柱状图强度
        double histogram = Math.abs(macd[2]);
        double prevHistogram = Math.abs(prevMacd[2]);
        double prev2Histogram = Math.abs(prev2Macd[2]);

        // 要求连续两个周期柱状图增长，更严格的条件
        boolean histogramGrowing = histogram > prevHistogram && prevHistogram > prev2Histogram;

        // 要求MACD值与信号线的距离足够大，表示趋势更明显
        double macdSignalDiff = Math.abs(macd[0] - macd[1]);
        boolean strongDivergence = macdSignalDiff > histogram * 0.5;

        if (crossSignal && histogramGrowing && strongDivergence) {
            return SIGNAL_STRENGTH_HIGH;
        }
        if (crossSignal && histogramGrowing) {
            return 0.8;
        }
        if (crossSignal) {
            return 0.5;
        }
        if (histogramGrowing) {
            return 0.3;
        }
        return SIGNAL_STRENGTH_LOW;
    }

    /**
     * 计算布林带强度
     */
    private double calculateBollingerStrength(List<KLineEntity> klines, boolean isLong) {
        if (klines.size() < BOLLINGER_PERIOD) {
            return SIGNAL_STRENGTH_LOW;
        }

        List<Double> prices = extractClosePrices(klines);

        double sma = prices.subList(0, BOLLINGER_PERIOD).stream()
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0);

        double stdDev = Math.sqrt(prices.subList(0, BOLLINGER_PERIOD).stream()
            .mapToDouble(p -> Math.pow(p - sma, 2))
            .average()
            .orElse(0));

        double currentPrice = prices.get(0);
        double lowerBand = sma - BOLLINGER_STD_DEV * stdDev;
        double upperBand = sma + BOLLINGER_STD_DEV * stdDev;

        if (isLong) {
            if (currentPrice <= lowerBand * BOLLINGER_CLOSE_FACTOR) {
                return SIGNAL_STRENGTH_HIGH;
            }
            if (currentPrice <= lowerBand * BOLLINGER_MEDIUM_FACTOR) {
                return 0.8;
            }
            if (currentPrice <= sma) {
                return 0.5;
            }
        } else {
            if (currentPrice >= upperBand * (2 - BOLLINGER_CLOSE_FACTOR)) {
                return SIGNAL_STRENGTH_HIGH;
            }
            if (currentPrice >= upperBand * (2 - BOLLINGER_MEDIUM_FACTOR)) {
                return 0.8;
            }
            if (currentPrice >= sma) {
                return 0.5;
            }
        }

        return SIGNAL_STRENGTH_LOW;
    }

    /**
     * 计算K线形态强度
     */
    private double calculatePatternStrength(List<KLineEntity> klines, boolean isLong) {
        if (klines.size() < 3) return 0;

        KLineEntity current = klines.get(0);
        KLineEntity prev = klines.get(1);

        double bodySize = Math.abs(current.getClose().doubleValue() - current.getOpen().doubleValue());
        double totalSize = current.getHigh().doubleValue() - current.getLow().doubleValue();

        if (isLong) {
            // 看涨形态
            boolean bullishCandle = current.getClose().doubleValue() > current.getOpen().doubleValue();
            boolean strongBody = bodySize > totalSize * 0.6;
            boolean bottomWick = (current.getClose().doubleValue() - current.getLow().doubleValue()) > bodySize;

            if (bullishCandle && strongBody && bottomWick) return 1.0;
            if (bullishCandle && strongBody) return 0.8;
            if (bullishCandle) return 0.5;
        } else {
            // 看跌形态
            boolean bearishCandle = current.getClose().doubleValue() < current.getOpen().doubleValue();
            boolean strongBody = bodySize > totalSize * 0.6;
            boolean topWick = (current.getHigh().doubleValue() - current.getOpen().doubleValue()) > bodySize;

            if (bearishCandle && strongBody && topWick) return 1.0;
            if (bearishCandle && strongBody) return 0.8;
            if (bearishCandle) return 0.5;
        }

        return 0.0;
    }

    /**
     * 计算价格突破强度
     */
    private double calculatePriceBreakoutStrength(List<KLineEntity> klines, boolean isLong) {
        if (klines.size() < BREAKOUT_LOOKBACK_PERIOD) {
            return SIGNAL_STRENGTH_LOW;
        }

        double currentPrice = klines.get(0).getClose().doubleValue();
        double maxPrice = klines.subList(1, BREAKOUT_LOOKBACK_PERIOD).stream()
            .mapToDouble(k -> k.getHigh().doubleValue())
            .max()
            .orElse(0);
        double minPrice = klines.subList(1, BREAKOUT_LOOKBACK_PERIOD).stream()
            .mapToDouble(k -> k.getLow().doubleValue())
            .min()
            .orElse(0);

        if (isLong) {
            if (currentPrice > maxPrice) {
                return SIGNAL_STRENGTH_HIGH;
            }
            double range = maxPrice - minPrice;
            return range > 0 ? (currentPrice - minPrice) / range : SIGNAL_STRENGTH_LOW;
        } else {
            if (currentPrice < minPrice) {
                return SIGNAL_STRENGTH_HIGH;
            }
            double range = maxPrice - minPrice;
            return range > 0 ? (maxPrice - currentPrice) / range : SIGNAL_STRENGTH_LOW;
        }
    }

    /**
     * 计算技术指标综合得分
     */
    private double calculateTechnicalScore(double macdStrength, double rsiStrength, double bollingerStrength) {
        return (macdStrength * 0.4 + rsiStrength * 0.3 + bollingerStrength * 0.3);
    }

    /**
     * 计算形态分析得分
     */
    private double calculatePatternScore(double patternStrength, double breakoutStrength) {
        return (patternStrength * 0.6 + breakoutStrength * 0.4);
    }

    /**
     * 入场及止盈止损设置
     */
    private void processEntry(String instId, BigDecimal currentPrice,
                              List<KLineEntity> klineList, boolean isLong, double signalStrength) {
        try {
            log.info("[短线策略]准备{}入场: instId={}, 当前价格={}",
                isLong ? "多头" : "空头", instId, currentPrice);

            // 动态调整ATR倍数（信号强度越高，止损越宽松，止盈越激进）
            double slMultiplier = STOP_LOSS_ATR_MULTIPLIER * (1 + (signalStrength - 0.5) * 0.4);
            double tpMultiplier = TAKE_PROFIT_SECOND_ATR_MULTIPLIER * (1 + (signalStrength - 0.5) * 0.4);

            StopLevelDTO stopLevels = calculateStopLevels(instId, currentPrice,
                klineList, isLong,
                slMultiplier, tpMultiplier);
            if (stopLevels == null) return;

            // 计算风险控制的仓位大小
            String sz = getSz(instId, currentPrice.doubleValue());

            // 下单
            String side = isLong ? "buy" : "sell";
            String posSide = isLong ? "long" : "short";
            trade(instId, side, sz, posSide, false);
            Thread.sleep(100);

            // 设置止盈止损
            setupStopOrders(instId, currentPrice, stopLevels, isLong);
        } catch (Exception e) {
            log.error("[短线策略]执行入场操作异常", e);
        }
    }

    /**
     * 计算止盈止损价格，支持动态调整
     */
    private StopLevelDTO calculateStopLevels(String instId, BigDecimal currentPrice,
                                             List<KLineEntity> klineList, boolean isLong,
                                             double slMultiplier, double tpMultiplier) {
        double atr = calculateATR(klineList, 0, ATR_PERIOD);
        if (atr <= 0) {
            log.info("ATR计算错误，取消入场");
            return null;
        }
        
        // 计算ADX - 新增
        ADXResult adxResult = calculateADX(klineList, ADX_PERIOD);
        
        // 根据ADX调整止盈止损倍数 - 新增
        double adxSlFactor = 1.0;
        double adxTpFactor = 1.0;
        
        if (adxResult.hasTrend()) {
            boolean adxBullish = adxResult.isBullishTrend();
            double trendStrength = adxResult.getTrendStrength();
            
            if ((isLong && adxBullish) || (!isLong && !adxBullish)) {
                // 趋势方向与交易方向一致，可以放宽止损，提高止盈
                adxSlFactor = 1.1 + trendStrength * 0.2; // 最多增加30%
                adxTpFactor = 1.2 + trendStrength * 0.3; // 最多增加50%
                log.info("[{}] ADX显示趋势方向一致({}), 调整止损因子={}, 止盈因子={}", 
                    instId, adxResult.getTrend(), 
                    String.format("%.2f", adxSlFactor), 
                    String.format("%.2f", adxTpFactor));
            } else {
                // 趋势方向与交易方向相反，收紧止损，降低止盈
                adxSlFactor = 0.9 - trendStrength * 0.2; // 最多减少30%
                adxTpFactor = 0.8 - trendStrength * 0.2; // 最多减少40%
                log.info("[{}] ADX显示趋势方向相反({}), 调整止损因子={}, 止盈因子={}", 
                    instId, adxResult.getTrend(), 
                    String.format("%.2f", adxSlFactor), 
                    String.format("%.2f", adxTpFactor));
            }
        }

        // 动态调整止盈止损倍数（如波动率高则收紧）
        double volatility = calculateVolatility(klineList);
        double volatilityFactor = volatility > 1.2 ? 0.8 : (volatility < 0.8 ? 1.2 : 1.0);

        // 根据市场波动率和ADX调整倍数
        double finalSlMultiplier = slMultiplier * volatilityFactor * adxSlFactor;
        double finalTpMultiplier = tpMultiplier * volatilityFactor * adxTpFactor;

        // 计算三级止盈倍数
        double firstTpMultiplier = TAKE_PROFIT_FIRST_ATR_MULTIPLIER * volatilityFactor * adxTpFactor;
        double secondTpMultiplier = TAKE_PROFIT_SECOND_ATR_MULTIPLIER * volatilityFactor * adxTpFactor;
        double thirdTpMultiplier = TAKE_PROFIT_THIRD_ATR_MULTIPLIER * volatilityFactor * adxTpFactor;

        // 根据方向设置止盈止损
        BigDecimal stopLossPrice, firstTakeProfitPrice, secondTakeProfitPrice, thirdTakeProfitPrice;
        BigDecimal currentPriceBD = currentPrice;

        if (isLong) {
            // 计算止损价格
            BigDecimal atrBasedStop = currentPriceBD.subtract(new BigDecimal(atr * finalSlMultiplier));
            BigDecimal percentBasedStop = currentPriceBD.multiply(new BigDecimal(1 - STOP_LOSS_MIN_PERCENT));
            // 取两者中较大的值作为止损价（更接近当前价格）
            stopLossPrice = atrBasedStop.compareTo(percentBasedStop) > 0 ? atrBasedStop : percentBasedStop;
            stopLossPrice = stopLossPrice.setScale(currentPrice.scale(), RoundingMode.DOWN);

            // 计算三级止盈价格
            firstTakeProfitPrice = currentPriceBD.add(new BigDecimal(atr * firstTpMultiplier))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
            secondTakeProfitPrice = currentPriceBD.add(new BigDecimal(atr * secondTpMultiplier))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
            thirdTakeProfitPrice = currentPriceBD.add(new BigDecimal(atr * thirdTpMultiplier))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
        } else {
            // 计算止损价格
            BigDecimal atrBasedStop = currentPriceBD.add(new BigDecimal(atr * finalSlMultiplier));
            BigDecimal percentBasedStop = currentPriceBD.multiply(new BigDecimal(1 + STOP_LOSS_MIN_PERCENT));
            // 取两者中较小的值作为止损价（更接近当前价格）
            stopLossPrice = atrBasedStop.compareTo(percentBasedStop) < 0 ? atrBasedStop : percentBasedStop;
            stopLossPrice = stopLossPrice.setScale(currentPrice.scale(), RoundingMode.DOWN);

            // 计算三级止盈价格
            firstTakeProfitPrice = currentPriceBD.subtract(new BigDecimal(atr * firstTpMultiplier))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
            secondTakeProfitPrice = currentPriceBD.subtract(new BigDecimal(atr * secondTpMultiplier))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
            thirdTakeProfitPrice = currentPriceBD.subtract(new BigDecimal(atr * thirdTpMultiplier))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
        }

        // 检查止盈止损比例是否合理
        double riskRewardRatio = calculateRiskRewardRatio(currentPrice.doubleValue(),
            stopLossPrice.doubleValue(), thirdTakeProfitPrice.doubleValue());

        if (riskRewardRatio < MIN_RISK_REWARD_RATIO) {
            log.info("风险收益比过低: {}, 取消入场", riskRewardRatio);
            return null;
        }
        
        // 记录ADX信息 - 新增
        log.info("[{}] 入场ADX={} ({}), 方向={}, 止损倍数={}, 止盈倍数={}",
            instId, 
            String.format("%.1f", adxResult.getAdx()),
            adxResult.getTrend(),
            adxResult.isBullishTrend() ? "多" : "空",
            String.format("%.2f", finalSlMultiplier),
            String.format("%.2f", finalTpMultiplier));

        return new StopLevelDTO(stopLossPrice, firstTakeProfitPrice, secondTakeProfitPrice, thirdTakeProfitPrice);
    }

    /**
     * 计算风险收益比
     */
    private double calculateRiskRewardRatio(double currentPrice, double stopLossPrice, double takeProfitPrice) {
        double risk = Math.abs(currentPrice - stopLossPrice) / currentPrice;
        double reward = Math.abs(takeProfitPrice - currentPrice) / currentPrice;
        return reward / risk;
    }

    /**
     * 设置止盈止损委托
     */
    private void setupStopOrders(String instId, BigDecimal currentPrice,
                                 StopLevelDTO stopLevels, boolean isLong) {
        Position pos = getPos(instId);
        if (pos == null) {
            log.info("未获取到持仓信息，无法设置止盈止损");
            return;
        }

        String stopLossPx = stopLevels.stopLossPrice.toString();
        String firstTakeProfitPx = stopLevels.firstTakeProfitPrice.toString();
        String secondTakeProfitPx = stopLevels.secondTakeProfitPrice.toString();
        String thirdTakeProfitPx = stopLevels.thirdTakeProfitPrice.toString();

        String side = isLong ? "sell" : "buy";
        String posSide = isLong ? "long" : "short";
        String availPos = pos.getAvailPos();

        // 计算三级止盈的仓位大小
        BigDecimal totalPos = new BigDecimal(availPos);

        // 第一级止盈仓位 (40%)
        BigDecimal firstTpPos = totalPos.multiply(new BigDecimal(FIRST_TP_POSITION_PERCENT))
            .setScale(new BigDecimal(availPos).scale(), RoundingMode.DOWN);
        String firstTpSz = firstTpPos.toString();

        // 第二级止盈仓位 (30%)
        BigDecimal secondTpPos = totalPos.multiply(new BigDecimal(SECOND_TP_POSITION_PERCENT))
            .setScale(new BigDecimal(availPos).scale(), RoundingMode.DOWN);
        String secondTpSz = secondTpPos.toString();

        // 第三级止盈仓位 (剩余30%)
        BigDecimal thirdTpPos = totalPos.subtract(firstTpPos).subtract(secondTpPos);
        String thirdTpSz = thirdTpPos.toString();

        // 设置止损
        algoTradeLoss(instId, side, availPos, posSide, stopLossPx);

        // 设置第一级止盈（部分仓位）
        if (firstTpPos.compareTo(BigDecimal.ZERO) > 0) {
            algoTradeWin(instId, side, firstTpSz, posSide, firstTakeProfitPx);
        }

        // 设置第二级止盈（部分仓位）
        if (secondTpPos.compareTo(BigDecimal.ZERO) > 0) {
            algoTradeWin(instId, side, secondTpSz, posSide, secondTakeProfitPx);
        }

        // 设置第三级止盈（剩余仓位）
        if (thirdTpPos.compareTo(BigDecimal.ZERO) > 0) {
            algoTradeWin(instId, side, thirdTpSz, posSide, thirdTakeProfitPx);
        }

        // 记录交易信息到Redis - 使用工厂方法创建TradeInfo对象
        TradeInfo tradeInfo = createTradeInfo(
            instId, 
            posSide, 
            pos.getPosId(),
            Double.parseDouble(pos.getAvgPx()),
            Double.parseDouble(stopLossPx),
            Double.parseDouble(firstTakeProfitPx),
            Double.parseDouble(secondTakeProfitPx),
            Double.parseDouble(thirdTakeProfitPx),
            Double.parseDouble(availPos)
        );

        String key = STRATEGY_KEY_PREFIX + instId;
        com.miner.common.utils.redis.RedisUtils.setCacheObject(key, tradeInfo, Duration.ofHours(4));

        // 通知
        String title = String.format("短线%s[%s]", isLong ? "多头" : "空头", instId);
        String message = MessageFormat.format("{0}{1}入场: 入场价={2}, 止损={3}, 一级止盈={4}({5}%), 二级止盈={6}({7}%), 三级止盈={8}({9}%)",
            instId, isLong ? "多头" : "空头",
            new BigDecimal(pos.getAvgPx()).setScale(currentPrice.scale(), RoundingMode.DOWN),
            stopLevels.stopLossPrice,
            stopLevels.firstTakeProfitPrice,
            Math.round(FIRST_TP_POSITION_PERCENT * 100),
            stopLevels.secondTakeProfitPrice,
            Math.round(SECOND_TP_POSITION_PERCENT * 100),
            stopLevels.thirdTakeProfitPrice,
            Math.round((1 - FIRST_TP_POSITION_PERCENT - SECOND_TP_POSITION_PERCENT) * 100));
        messageService.send(title, message);
    }

    /**
     * 计算ATR
     */
    private double calculateATR(List<KLineEntity> klineList, int offset, int period) {
        if (klineList.size() < offset + period + 1) {
            return 0;
        }
        double[] trValues = new double[period];
        for (int i = 0; i < period; i++) {
            int currIdx = offset + i;
            int prevIdx = offset + i + 1;
            KLineEntity curr = klineList.get(currIdx);
            KLineEntity prev = klineList.get(prevIdx);
            double high = curr.getHigh().doubleValue();
            double low = curr.getLow().doubleValue();
            double prevClose = prev.getClose().doubleValue();
            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            trValues[i] = Math.max(Math.max(tr1, tr2), tr3);
        }
        double sum = 0;
        for (double tr : trValues) {
            sum += tr;
        }
        return sum / period;
    }

    /**
     * 计算波动率（标准差/均值）
     */
    private double calculateVolatility(List<KLineEntity> klineList) {
        if (klineList.size() < 20) return 1.0;
        double sum = 0, sum2 = 0;
        int n = 20;
        for (int i = 0; i < n; i++) {
            double close = klineList.get(i).getClose().doubleValue();
            sum += close;
            sum2 += close * close;
        }
        double mean = sum / n;
        double std = Math.sqrt(sum2 / n - mean * mean);
        return std / mean * 100;
    }

    /**
     * 计算EMA指标
     */
    private double calculateEMA(List<Double> prices, int offset, int period) {
        if (prices.size() < offset + period) {
            return 0;
        }
        double alpha = 2.0 / (period + 1);
        double ema = prices.get(offset + period - 1);
        for (int i = offset + period - 2; i >= offset; i--) {
            ema = alpha * prices.get(i) + (1 - alpha) * ema;
        }
        return ema;
    }

    /**
     * 计算MACD
     */
    private double[] calculateMACD(List<Double> prices) {
        double fastEMA = calculateEMA(prices, 0, MACD_FAST);
        double slowEMA = calculateEMA(prices, 0, MACD_SLOW);
        double macdLine = fastEMA - slowEMA;

        List<Double> macdValues = new java.util.ArrayList<>();
        for (int i = 0; i < Math.min(MACD_SIGNAL, prices.size()); i++) {
            double fastEMAi = calculateEMA(prices, i, MACD_FAST);
            double slowEMAi = calculateEMA(prices, i, MACD_SLOW);
            macdValues.add(fastEMAi - slowEMAi);
        }

        double signalLine = calculateEMA(macdValues, 0, MACD_SIGNAL);
        double histogram = macdLine - signalLine;

        return new double[]{macdLine, signalLine, histogram};
    }

    /**
     * 取消订单
     */
    private void cancelAlgoOrders(String instId) {
        try {
            // 获取未完成的算法委托单列表
            JSONObject algoOrderList = tradeAPIService.getAlgoOrderList(null, null, instId, null, null, null, "50");
            if (algoOrderList != null && "0".equals(algoOrderList.getString("code"))) {
                JSONArray dataArray = algoOrderList.getJSONArray("data");
                if (dataArray != null && dataArray.size() > 0) {
                    // 准备取消参数
                    List<com.miner.system.okx.bean.trade.param.CancelAlgoOrder> cancelList = new ArrayList<>();

                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject algoOrder = dataArray.getJSONObject(i);
                        String algoId = algoOrder.getString("algoId");
                        if (StrUtil.isNotBlank(algoId)) {
                            com.miner.system.okx.bean.trade.param.CancelAlgoOrder cancelAlgoOrder = new com.miner.system.okx.bean.trade.param.CancelAlgoOrder();
                            cancelAlgoOrder.setAlgoId(algoId);
                            cancelAlgoOrder.setInstId(instId);
                            cancelList.add(cancelAlgoOrder);
                        }
                    }

                    // 执行取消操作
                    if (!cancelList.isEmpty()) {
                        JSONObject result = tradeAPIService.cancelAlgoOrder(cancelList);
                        if (result != null && "0".equals(result.getString("code"))) {
                            log.info("[{}] 成功取消{}个算法委托单", instId, cancelList.size());
                        } else {
                            log.warn("[{}] 取消算法委托单可能失败: {}", instId, result != null ? result.toJSONString() : "无响应");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[{}] 取消算法委托单出错: {}", instId, e.getMessage());
        }
    }

    /**
     * 停止价格DTO
     */
    @Data
    private static class StopLevelDTO {
        private final BigDecimal stopLossPrice;
        private final BigDecimal firstTakeProfitPrice;
        private final BigDecimal secondTakeProfitPrice;
        private final BigDecimal thirdTakeProfitPrice;

        public StopLevelDTO(BigDecimal stopLossPrice, BigDecimal firstTakeProfitPrice, BigDecimal secondTakeProfitPrice, BigDecimal thirdTakeProfitPrice) {
            this.stopLossPrice = stopLossPrice;
            this.firstTakeProfitPrice = firstTakeProfitPrice;
            this.secondTakeProfitPrice = secondTakeProfitPrice;
            this.thirdTakeProfitPrice = thirdTakeProfitPrice;
        }
    }

    /**
     * 信号强度结果
     */
    @Data
    private static class SignalResult {
        private final boolean isValid;
        private final double strength;

        public SignalResult(boolean isValid, double strength) {
            this.isValid = isValid;
            this.strength = strength;
        }
    }

    /**
     * 交易信息
     */
    @Data
    private static class TradeInfo {
        private String instId;
        private String direction;
        private String posId;
        private double entryPrice;
        private long entryTime;
        private double firstTakeProfitPrice;    // 第一级止盈价格
        private double secondTakeProfitPrice;   // 第二级止盈价格
        private double thirdTakeProfitPrice;    // 第三级止盈价格
        private double stopLossPrice;
        private boolean firstTpExecuted;        // 第一级止盈是否已执行
        private boolean secondTpExecuted;       // 第二级止盈是否已执行
        private double originalPosition;        // 初始仓位大小
        private double currentPosition;         // 当前仓位大小
        private int trailingStopUpdates;        // 移动止损更新次数
        private double maxPnlRatio;             // 最大盈利比例
        private double initialStopLossPrice;    // 初始止损价格
        private long lastUpdateTime;            // 上次更新时间
    }

    /**
     * 检查是否已有交易
     */
    private boolean checkTrade(String key) {
        try {
            Object pos = com.miner.common.utils.redis.RedisUtils.getCacheObject(key);
            if (pos != null) {
                return true;
            }
        } catch (Exception e) {
            log.error("检查交易状态异常", e);
        }
        return false;
    }

    /**
     * 计算RSI（收盘价列表版本）
     */
    private double calculateRSI(List<Double> prices, int offset, int period) {
        if (prices.size() < period + offset + 1) {
            return 50.0; // 默认返回中性值
        }
        double sumGain = 0.0;
        double sumLoss = 0.0;
        for (int i = offset + 1; i <= offset + period; i++) {
            double change = prices.get(i - 1) - prices.get(i);
            if (change > 0) {
                sumGain += change;
            } else {
                sumLoss -= change;
            }
        }
        if (sumLoss == 0) {
            return 100.0;
        }
        double rs = sumGain / sumLoss;
        return 100.0 - (100.0 / (1.0 + rs));
    }

    /**
     * 计算长周期趋势强度
     */
    private double calculateTrendStrength(List<KLineEntity> klineLong, List<KLineEntity> kline4h, boolean isLong) {
        if (klineLong.size() < 50 || kline4h.size() < 20) return 0.5; // 数据不足时给中性评分

        // 计算30分钟周期的EMA趋势（20和50周期）
        List<Double> prices30m = klineLong.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        double ema20_30m = calculateEMA(prices30m, 0, 20);
        double ema50_30m = calculateEMA(prices30m, 0, 50);
        double ema20Prev_30m = calculateEMA(prices30m, 10, 20);
        double ema50Prev_30m = calculateEMA(prices30m, 10, 50);

        // 计算4小时周期的EMA趋势（10和30周期）
        List<Double> prices4h = kline4h.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        double ema10_4h = calculateEMA(prices4h, 0, 10);
        double ema30_4h = calculateEMA(prices4h, 0, 30);
        double ema10Prev_4h = calculateEMA(prices4h, 5, 10);
        double ema30Prev_4h = calculateEMA(prices4h, 5, 30);

        // 1. 计算30分钟周期EMA斜率
        double ema20Slope_30m = (ema20_30m - ema20Prev_30m) / ema20Prev_30m * 100; // 百分比变化
        double ema50Slope_30m = (ema50_30m - ema50Prev_30m) / ema50Prev_30m * 100;

        // 2. 计算4小时周期EMA斜率
        double ema10Slope_4h = (ema10_4h - ema10Prev_4h) / ema10Prev_4h * 100;
        double ema30Slope_4h = (ema30_4h - ema30Prev_4h) / ema30Prev_4h * 100;

        // 3. 判断30分钟周期EMA多头/空头排列
        boolean bullishAlignment_30m = ema20_30m > ema50_30m;
        boolean bearishAlignment_30m = ema20_30m < ema50_30m;

        // 4. 判断4小时周期EMA多头/空头排列
        boolean bullishAlignment_4h = ema10_4h > ema30_4h;
        boolean bearishAlignment_4h = ema10_4h < ema30_4h;

        // 5. 计算趋势强度得分
        double strength30m = 0.0;
        double strength4h = 0.0;

        // 30分钟周期趋势强度
        if (isLong) {
            // 多头信号
            if (bullishAlignment_30m && ema20Slope_30m > 0 && ema50Slope_30m > 0) {
                // 强多头趋势：EMA20在EMA50上方且都向上
                strength30m = 0.8 + Math.min(ema20Slope_30m, 0.2); // 最高1.0
            } else if (bullishAlignment_30m) {
                // EMA20在EMA50上方但斜率不全向上
                strength30m = 0.6;
            } else if (ema20Slope_30m > 0) {
                // EMA20向上但尚未上穿EMA50
                strength30m = 0.4;
            }
        } else {
            // 空头信号
            if (bearishAlignment_30m && ema20Slope_30m < 0 && ema50Slope_30m < 0) {
                // 强空头趋势：EMA20在EMA50下方且都向下
                strength30m = 0.8 + Math.min(Math.abs(ema20Slope_30m), 0.2); // 最高1.0
            } else if (bearishAlignment_30m) {
                // EMA20在EMA50下方但斜率不全向下
                strength30m = 0.6;
            } else if (ema20Slope_30m < 0) {
                // EMA20向下但尚未下穿EMA50
                strength30m = 0.4;
            }
        }

        // 4小时周期趋势强度
        if (isLong) {
            // 多头信号
            if (bullishAlignment_4h && ema10Slope_4h > 0 && ema30Slope_4h > 0) {
                // 强多头趋势：EMA10在EMA30上方且都向上
                strength4h = 0.8 + Math.min(ema10Slope_4h, 0.2); // 最高1.0
            } else if (bullishAlignment_4h) {
                // EMA10在EMA30上方但斜率不全向上
                strength4h = 0.6;
            } else if (ema10Slope_4h > 0) {
                // EMA10向上但尚未上穿EMA30
                strength4h = 0.4;
            }
        } else {
            // 空头信号
            if (bearishAlignment_4h && ema10Slope_4h < 0 && ema30Slope_4h < 0) {
                // 强空头趋势：EMA10在EMA30下方且都向下
                strength4h = 0.8 + Math.min(Math.abs(ema10Slope_4h), 0.2); // 最高1.0
            } else if (bearishAlignment_4h) {
                // EMA10在EMA30下方但斜率不全向下
                strength4h = 0.6;
            } else if (ema10Slope_4h < 0) {
                // EMA10向下但尚未下穿EMA30
                strength4h = 0.4;
            }
        }

        // 6. 计算趋势一致性加分
        double alignmentBonus = 0.0;
        if ((isLong && bullishAlignment_30m && bullishAlignment_4h) || 
            (!isLong && bearishAlignment_30m && bearishAlignment_4h)) {
            // 两个时间周期趋势方向一致，加分
            alignmentBonus = 0.2;
        }

        // 7. 计算最终趋势强度 (30分钟权重0.4，4小时权重0.6，加上一致性奖励)
        return Math.min(1.0, strength30m * 0.4 + strength4h * 0.6 + alignmentBonus);
    }

    /**
     * 计算支撑/阻力位强度
     * 分析价格是否在长周期的关键支撑/阻力位附近
     */
    private double calculateSupportResistanceStrength(List<KLineEntity> klineLong, double currentPrice, boolean isLong) {
        if (klineLong.size() < 30) return 0.0;

        // 1. 寻找最近的支撑/阻力位
        List<Double> supportLevels = new ArrayList<>();
        List<Double> resistanceLevels = new ArrayList<>();

        // 简化实现：使用近期低点作为支撑，高点作为阻力
        for (int i = 5; i < klineLong.size() - 5; i++) {
            KLineEntity current = klineLong.get(i);
            boolean isSupport = true;
            boolean isResistance = true;

            // 检查前后5根K线，判断是否为低/高点
            for (int j = i - 5; j <= i + 5; j++) {
                if (j == i) continue;

                if (klineLong.get(j).getLow().doubleValue() <= current.getLow().doubleValue()) {
                    isSupport = false;
                }

                if (klineLong.get(j).getHigh().doubleValue() >= current.getHigh().doubleValue()) {
                    isResistance = false;
                }
            }

            if (isSupport) supportLevels.add(current.getLow().doubleValue());
            if (isResistance) resistanceLevels.add(current.getHigh().doubleValue());
        }

        // 2. 计算当前价格与最近支撑/阻力位的关系
        double nearestSupportDistance = Double.MAX_VALUE;
        double nearestResistanceDistance = Double.MAX_VALUE;

        for (Double support : supportLevels) {
            double distance = Math.abs((currentPrice - support) / support);
            if (distance < nearestSupportDistance) {
                nearestSupportDistance = distance;
            }
        }

        for (Double resistance : resistanceLevels) {
            double distance = Math.abs((resistance - currentPrice) / currentPrice);
            if (distance < nearestResistanceDistance) {
                nearestResistanceDistance = distance;
            }
        }

        // 3. 评分逻辑
        if (isLong) {
            // 多头：价格接近支撑位时给高分
            if (nearestSupportDistance < 0.01) return 1.0; // 1%以内
            if (nearestSupportDistance < 0.02) return 0.8; // 2%以内
            if (nearestSupportDistance < 0.03) return 0.6; // 3%以内
            if (nearestSupportDistance < 0.05) return 0.4; // 5%以内
        } else {
            // 空头：价格接近阻力位时给高分
            if (nearestResistanceDistance < 0.01) return 1.0;
            if (nearestResistanceDistance < 0.02) return 0.8;
            if (nearestResistanceDistance < 0.03) return 0.6;
            if (nearestResistanceDistance < 0.05) return 0.4;
        }

        return 0.0;
    }

    /**
     * 计算长短周期协同强度
     * 分析长短周期的一致性程度
     */
    private double calculateCrossTimeframeStrength(List<KLineEntity> klineShort, List<KLineEntity> klineLong, List<KLineEntity> kline4h, boolean isLong) {
        double strength = 0.0;

        // 1. 短周期交易量是否高于长周期平均
        double shortTermVolume = klineShort.get(0).getVolume();
        double longTermAvgVolume = klineLong.subList(0, 10).stream()
            .mapToDouble(KLineEntity::getVolume)
            .average()
            .orElse(0);

        boolean volumeConsistency = shortTermVolume > longTermAvgVolume * 1.3;

        // 2. 三周期RSI协同性
        List<Double> shortPrices = klineShort.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        List<Double> longPrices = klineLong.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());
            
        List<Double> longTermPrices = kline4h.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(Collectors.toList());

        double shortRsi = calculateRSI(shortPrices, 0, 14);
        double longRsi = calculateRSI(longPrices, 0, 14);
        double longTermRsi = calculateRSI(longTermPrices, 0, 14);

        // RSI三周期协同 - 多头信号：短期超卖，中期和长期偏空
        // RSI三周期协同 - 空头信号：短期超买，中期和长期偏多
        boolean rsiConsistency = isLong ?
            (shortRsi < 40 && longRsi < 50 && longTermRsi < 50) : 
            (shortRsi > 60 && longRsi > 50 && longTermRsi > 50);
            
        // 强RSI协同 - 所有周期都明显偏向一个方向
        boolean strongRsiConsistency = isLong ?
            (shortRsi < 30 && longRsi < 40 && longTermRsi < 45) : 
            (shortRsi > 70 && longRsi > 60 && longTermRsi > 55);

        // 3. 三周期价格位置一致性
        double shortMA20 = calculateEMA(shortPrices, 0, 20);
        double longMA50 = calculateEMA(longPrices, 0, 50);
        double longTermMA100 = calculateEMA(longTermPrices, 0, 50);

        // 价格位置协同 - 多头：短期回调但仍在上升趋势中，中期和长期在均线上方
        // 价格位置协同 - 空头：短期反弹但仍在下降趋势中，中期和长期在均线下方
        boolean priceConsistency = isLong ?
            (klineShort.get(0).getClose().doubleValue() < shortMA20 &&
                klineLong.get(0).getClose().doubleValue() > longMA50 &&
                kline4h.get(0).getClose().doubleValue() > longTermMA100) : 
            (klineShort.get(0).getClose().doubleValue() > shortMA20 &&
                klineLong.get(0).getClose().doubleValue() < longMA50 &&
                kline4h.get(0).getClose().doubleValue() < longTermMA100);

        // 4. 三周期MACD协同性
        double[] shortMacd = calculateMACD(shortPrices);
        double[] longMacd = calculateMACD(longPrices);
        double[] longTermMacd = calculateMACD(longTermPrices);

        // MACD协同 - 多头：短期柱状图>0或金叉，中期DIF>DEA，长期DIF上升
        // MACD协同 - 空头：短期柱状图<0或死叉，中期DIF<DEA，长期DIF下降
        boolean macdConsistency = isLong ?
            (shortMacd[2] > 0 && longMacd[0] > longMacd[1] && 
             (longTermMacd[0] > longTermMacd[0] - longTermMacd[1])) : 
            (shortMacd[2] < 0 && longMacd[0] < longMacd[1] && 
             (longTermMacd[0] < longTermMacd[0] - longTermMacd[1]));
             
        // 强MACD协同 - 所有周期MACD都明确指向同一方向
        boolean strongMacdConsistency = isLong ?
            (shortMacd[2] > 0 && shortMacd[0] > shortMacd[1] && 
             longMacd[2] > 0 && longTermMacd[0] > longTermMacd[1]) : 
            (shortMacd[2] < 0 && shortMacd[0] < shortMacd[1] && 
             longMacd[2] < 0 && longTermMacd[0] < longTermMacd[1]);

        // 5. 三周期趋势方向一致性
        boolean trendConsistency = false;
        
        // 计算三个周期的短期均线斜率
        double shortEMA10 = calculateEMA(shortPrices, 0, 10);
        double shortEMA10Prev = calculateEMA(shortPrices, 5, 10);
        double shortSlope = (shortEMA10 - shortEMA10Prev) / shortEMA10Prev;
        
        double longEMA20 = calculateEMA(longPrices, 0, 20);
        double longEMA20Prev = calculateEMA(longPrices, 5, 20);
        double longSlope = (longEMA20 - longEMA20Prev) / longEMA20Prev;
        
        double longTermEMA20 = calculateEMA(longTermPrices, 0, 20);
        double longTermEMA20Prev = calculateEMA(longTermPrices, 2, 20);
        double longTermSlope = (longTermEMA20 - longTermEMA20Prev) / longTermEMA20Prev;
        
        // 三周期趋势方向一致
        if (isLong) {
            trendConsistency = shortSlope > 0 && longSlope > 0 && longTermSlope > 0;
        } else {
            trendConsistency = shortSlope < 0 && longSlope < 0 && longTermSlope < 0;
        }

        // 6. 计算总得分
        int consistencyCount = 0;
        if (volumeConsistency) consistencyCount++;
        if (rsiConsistency) consistencyCount++;
        if (priceConsistency) consistencyCount++;
        if (macdConsistency) consistencyCount++;
        if (trendConsistency) consistencyCount++;
        
        // 强协同加分
        double strongConsistencyBonus = 0.0;
        if (strongRsiConsistency) strongConsistencyBonus += 0.1;
        if (strongMacdConsistency) strongConsistencyBonus += 0.1;

        // 至少2个协同因素得0.5分，3个得0.7分，4个得0.9分，全部协同得1.0分
        if (consistencyCount >= 5) return Math.min(1.0, 1.0 + strongConsistencyBonus);
        if (consistencyCount >= 4) return Math.min(1.0, 0.9 + strongConsistencyBonus);
        if (consistencyCount >= 3) return Math.min(1.0, 0.7 + strongConsistencyBonus);
        if (consistencyCount >= 2) return 0.5;
        if (consistencyCount >= 1) return 0.3;

        return 0.0;
    }

    /**
     * 计算长周期趋势得分
     */
    private double calculateTrendScore(double trendStrength, double supportResistanceStrength) {
        return trendStrength * 0.6 + supportResistanceStrength * 0.4;
    }

    /**
     * 检查止盈触发情况
     */
    private void checkTakeProfitExecution(String instId, TradeInfo tradeInfo, double currentPrice, boolean isLong) {
        // 如果第二级止盈已执行，不需要再检查前两级
        if (tradeInfo.isSecondTpExecuted()) {
            return;
        }

        // 如果第一级止盈已执行但第二级未执行，检查第二级
        if (tradeInfo.isFirstTpExecuted() && !tradeInfo.isSecondTpExecuted()) {
            double secondTpPrice = tradeInfo.getSecondTakeProfitPrice();
            boolean secondTpTriggered = isLong ?
                (currentPrice >= secondTpPrice) :
                (currentPrice <= secondTpPrice);

            if (secondTpTriggered) {
                tradeInfo.setSecondTpExecuted(true);
                updateTradeInfo(instId, tradeInfo);

                // 通知
                String message = MessageFormat.format("{0}第二级止盈已触发，{1}%仓位平仓，剩余{2}%仓位继续持有",
                    instId,
                    Math.round(SECOND_TP_POSITION_PERCENT * 100),
                    Math.round((1 - FIRST_TP_POSITION_PERCENT - SECOND_TP_POSITION_PERCENT) * 100));
                messageService.send("短线部分止盈", message);
            }
            return;
        }

        // 检查第一级止盈
        if (!tradeInfo.isFirstTpExecuted()) {
            double firstTpPrice = tradeInfo.getFirstTakeProfitPrice();
            boolean firstTpTriggered = isLong ?
                (currentPrice >= firstTpPrice) :
                (currentPrice <= firstTpPrice);

            if (firstTpTriggered) {
                tradeInfo.setFirstTpExecuted(true);
                updateTradeInfo(instId, tradeInfo);

                // 通知
                String message = MessageFormat.format("{0}第一级止盈已触发，{1}%仓位平仓，剩余{2}%仓位继续持有",
                    instId,
                    Math.round(FIRST_TP_POSITION_PERCENT * 100),
                    Math.round((1 - FIRST_TP_POSITION_PERCENT) * 100));
                messageService.send("短线部分止盈", message);
            }
        }
    }

    /**
     * 创建并初始化TradeInfo对象
     * 确保所有字段都被正确初始化，防止空指针异常
     */
    private TradeInfo createTradeInfo(String instId, String posSide, String posId, double entryPrice, 
                                     double stopLossPrice, double firstTpPrice, double secondTpPrice, 
                                     double thirdTpPrice, double positionSize) {
        TradeInfo tradeInfo = new TradeInfo();
        // 基本信息
        tradeInfo.setInstId(instId);
        tradeInfo.setDirection(posSide);
        tradeInfo.setPosId(posId);
        tradeInfo.setEntryPrice(entryPrice);
        tradeInfo.setEntryTime(System.currentTimeMillis());
        
        // 止盈止损价格
        tradeInfo.setStopLossPrice(stopLossPrice);
        tradeInfo.setInitialStopLossPrice(stopLossPrice);
        tradeInfo.setFirstTakeProfitPrice(firstTpPrice);
        tradeInfo.setSecondTakeProfitPrice(secondTpPrice);
        tradeInfo.setThirdTakeProfitPrice(thirdTpPrice);
        
        // 仓位信息
        tradeInfo.setOriginalPosition(positionSize);
        tradeInfo.setCurrentPosition(positionSize);
        
        // 状态信息
        tradeInfo.setFirstTpExecuted(false);
        tradeInfo.setSecondTpExecuted(false);
        tradeInfo.setTrailingStopUpdates(0);
        tradeInfo.setMaxPnlRatio(0.0);
        tradeInfo.setLastUpdateTime(System.currentTimeMillis());
        
        return tradeInfo;
    }

    /**
     * 分析波动周期特征
     */
    private double analyzeVolatilityCycle(List<KLineEntity> klines) {
        if (klines.size() < 30) return 0.0;

        // 计算波动率序列
        List<Double> volatilities = new ArrayList<>();
        for (int i = 0; i < klines.size() - 1; i++) {
            double range = (klines.get(i).getHigh().doubleValue() - klines.get(i).getLow().doubleValue()) /
                klines.get(i).getClose().doubleValue() * 100; // 转为百分比
            volatilities.add(range);
        }

        // 计算波动率的自相关性
        double correlation = calculateAutoCorrelation(volatilities, 5); // 5根K线的滞后期

        // 判断是否存在明显的周期性
        return Math.abs(correlation);  // 0-1之间，越大表示周期性越强
    }

    /**
     * 计算序列的自相关系数
     */
    private double calculateAutoCorrelation(List<Double> series, int lag) {
        if (series.size() < lag + 10) return 0.0;
        
        // 计算均值
        double mean = series.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

        // 计算自相关系数
        double numerator = 0.0;
        double denominator = 0.0;

        for (int i = 0; i < series.size() - lag; i++) {
            numerator += (series.get(i) - mean) * (series.get(i + lag) - mean);
            denominator += Math.pow(series.get(i) - mean, 2);
        }
        
        return numerator / denominator;
    }
    
    /**
     * 增强的成交量特征分析 - 整合三个周期的数据
     */
    private VolumeAnalysisResult analyzeVolumeFeatures(List<KLineEntity> klineShort,
                                                       List<KLineEntity> klineLong,
                                                       List<KLineEntity> kline4h,
                                                       boolean isLong) {
        if (klineShort.size() < 20 || klineLong.size() < 20 || kline4h.size() < 10) {
            return new VolumeAnalysisResult(0, 0, 0, 0, "数据不足");
        }

        // 1. 短周期(5分钟)成交量突破强度
        double volumeBreakoutStrength = calculateVolumeBreakout(klineShort);

        // 2. 买卖压力分析 - 结合短周期和中周期
        double shortTermBuyPressure = analyzeBuyPressure(klineShort, isLong);
        double longTermBuyPressure = analyzeBuyPressure(klineLong, isLong);
        // 加权平均，短周期权重更高
        double buyPressureStrength = shortTermBuyPressure * 0.7 + longTermBuyPressure * 0.3;

        // 3. 成交量形态分析 - 短周期
        double volumePatternStrength = analyzeVolumePattern(klineShort, isLong);

        // 4. 多周期VWAP趋势分析
        double shortVwapStrength = analyzeVwapTrend(klineShort, isLong);
        double longVwapStrength = analyzeVwapTrend(klineLong, isLong);
        // 加权平均，短周期权重更高
        double vwapTrendStrength = shortVwapStrength * 0.6 + longVwapStrength * 0.4;
        
        // 5. 长周期(4小时)成交量趋势分析
        double longTermVolumeTrend = analyzeLongTermVolumeTrend(kline4h, isLong);
        
        // 6. 成交量与价格背离分析
        double divergenceStrength = analyzeVolumePriceDivergence(klineShort, klineLong, isLong);

        // 生成日志信息
        String logInfo = String.format(
            "量突破=%.2f, 买压=%.2f, 量形态=%.2f, VWAP趋势=%.2f, 长期量趋势=%.2f, 背离=%.2f",
            volumeBreakoutStrength, buyPressureStrength,
            volumePatternStrength, vwapTrendStrength,
            longTermVolumeTrend, divergenceStrength
        );

        // 调整最终结果，加入长周期成交量趋势和背离分析
        double finalVolumeBreakoutStrength = volumeBreakoutStrength * 0.7 + longTermVolumeTrend * 0.3;
        double finalBuyPressureStrength = buyPressureStrength * 0.8 + divergenceStrength * 0.2;

        return new VolumeAnalysisResult(
            finalVolumeBreakoutStrength,
            finalBuyPressureStrength,
            volumePatternStrength,
            vwapTrendStrength,
            logInfo
        );
    }

    /**
     * 计算成交量突破强度
     */
    private double calculateVolumeBreakout(List<KLineEntity> klines) {
        if (klines.size() < VOLUME_STATS_PERIOD + 1) {
            return SIGNAL_STRENGTH_LOW;
        }

        double currentVolume = klines.get(0).getVolume();

        // 计算前指定根数K线的成交量统计
        DoubleSummaryStatistics stats = klines.subList(1, VOLUME_STATS_PERIOD + 1).stream()
            .mapToDouble(KLineEntity::getVolume)
            .summaryStatistics();

        double avgVolume = stats.getAverage();
        if (avgVolume == 0) {
            return SIGNAL_STRENGTH_LOW;
        }

        double stdDev = Math.sqrt(
            klines.subList(1, VOLUME_STATS_PERIOD + 1).stream()
                .mapToDouble(k -> Math.pow(k.getVolume() - avgVolume, 2))
                .average()
                .orElse(0)
        );

        if (stdDev == 0) {
            return currentVolume > avgVolume ? SIGNAL_STRENGTH_HIGH : SIGNAL_STRENGTH_LOW;
        }

        // 计算Z-score
        double zScore = (currentVolume - avgVolume) / stdDev;

        // 将Z-score转换为0-1的得分
        return Math.min(Math.max((zScore - 1) / 3, 0), 1);
    }

    /**
     * 分析买卖压力
     */
    private double analyzeBuyPressure(List<KLineEntity> klines, boolean isLong) {
        if (klines.size() < VOLUME_ANALYSIS_PERIOD) {
            return 0.5; // 中性值
        }

        double buyVolume = 0;
        double totalVolume = 0;

        // 统计最近指定根数K线的买卖量
        int analysisSize = Math.min(VOLUME_ANALYSIS_PERIOD, klines.size());
        for (int i = 0; i < analysisSize; i++) {
            KLineEntity k = klines.get(i);
            double close = k.getClose().doubleValue();
            double open = k.getOpen().doubleValue();
            double volume = k.getVolume();

            if (close > open) {
                buyVolume += volume;
            }
            totalVolume += volume;
        }

        if (totalVolume == 0) {
            return 0.5; // 中性值
        }

        double buyRatio = buyVolume / totalVolume;
        return isLong ? buyRatio : (1 - buyRatio);
    }

    /**
     * 分析成交量形态
     */
    private double analyzeVolumePattern(List<KLineEntity> klines, boolean isLong) {
        final int PATTERN_PERIOD = 5;
        if (klines.size() < PATTERN_PERIOD) {
            return SIGNAL_STRENGTH_LOW;
        }

        // 检查量价背离
        boolean priceHigher = klines.get(0).getClose().doubleValue() >
            klines.get(PATTERN_PERIOD - 1).getClose().doubleValue();

        double currentVolume = klines.get(0).getVolume();
        double prevVolume = klines.get(PATTERN_PERIOD - 1).getVolume();
        boolean volumeHigher = currentVolume > prevVolume;

        // 计算成交量的连续性
        int consecutiveHigherVolume = 0;
        for (int i = 1; i < PATTERN_PERIOD; i++) {
            if (klines.get(i - 1).getVolume() > klines.get(i).getVolume()) {
                consecutiveHigherVolume++;
            }
        }

        double continuityScore = (double) consecutiveHigherVolume / (PATTERN_PERIOD - 1);

        // 根据做多做空方向评分
        if (isLong) {
            if (priceHigher && volumeHigher) {
                return VOLUME_PATTERN_BASE_WEIGHT + VOLUME_PATTERN_CONTINUITY_WEIGHT * continuityScore;
            }
            return priceHigher ? 0.3 : SIGNAL_STRENGTH_LOW;
        } else {
            if (!priceHigher && volumeHigher) {
                return VOLUME_PATTERN_BASE_WEIGHT + VOLUME_PATTERN_CONTINUITY_WEIGHT * continuityScore;
            }
            return !priceHigher ? 0.3 : SIGNAL_STRENGTH_LOW;
        }
    }

    /**
     * 分析VWAP趋势
     */
    private double analyzeVwapTrend(List<KLineEntity> klines, boolean isLong) {
        if (klines.size() < 20) return 0.0;

        // 计算VWAP
        double sumPV = 0;
        double sumV = 0;
        List<Double> vwaps = new ArrayList<>();

        for (KLineEntity k : klines) {
            double typical = (k.getHigh().doubleValue() + k.getLow().doubleValue() +
                k.getClose().doubleValue()) / 3;
            double volume = k.getVolume();

            sumPV += typical * volume;
            sumV += volume;
            vwaps.add(sumPV / sumV);
        }

        // 计算VWAP斜率
        double currentVwap = vwaps.get(0);
        double prevVwap = vwaps.get(4);  // 5根K线前的VWAP

        double vwapSlope = (currentVwap - prevVwap) / prevVwap;

        // 价格相对VWAP的位置
        double currentPrice = klines.get(0).getClose().doubleValue();
        double priceToVwap = (currentPrice - currentVwap) / currentVwap;

        if (isLong) {
            return vwapSlope > 0 ?
                Math.min((vwapSlope * 100 + Math.max(priceToVwap, 0) * 50), 1.0) : 0.0;
        } else {
            return vwapSlope < 0 ?
                Math.min((Math.abs(vwapSlope) * 100 + Math.max(-priceToVwap, 0) * 50), 1.0) : 0.0;
        }
    }
    
    /**
     * 分析长周期(4小时)成交量趋势
     */
    private double analyzeLongTermVolumeTrend(List<KLineEntity> klines, boolean isLong) {
        if (klines.size() < 10) return 0.0;
        
        // 计算近期和较远期的平均成交量
        double recentAvgVolume = klines.subList(0, 5).stream()
            .mapToDouble(KLineEntity::getVolume)
            .average()
            .orElse(0);
            
        double olderAvgVolume = klines.subList(5, 10).stream()
            .mapToDouble(KLineEntity::getVolume)
            .average()
            .orElse(0);
            
        // 计算成交量趋势
        double volumeTrend = recentAvgVolume / olderAvgVolume - 1;
        
        // 根据多空方向评分
        if (isLong) {
            // 多头信号：成交量上升趋势
            return volumeTrend > 0 ? Math.min(volumeTrend * 2, 1.0) : 0.0;
        } else {
            // 空头信号：成交量下降趋势或异常放量
            return (volumeTrend < 0 || volumeTrend > 0.5) ? 
                Math.min(Math.abs(volumeTrend) * 2, 1.0) : 0.0;
        }
    }
    
    /**
     * 分析成交量与价格背离
     */
    private double analyzeVolumePriceDivergence(List<KLineEntity> klineShort, 
                                               List<KLineEntity> klineLong, 
                                               boolean isLong) {
        if (klineShort.size() < 10 || klineLong.size() < 10) return 0.0;
        
        // 短周期价格趋势
        double shortPriceChange = (klineShort.get(0).getClose().doubleValue() / 
                                  klineShort.get(9).getClose().doubleValue()) - 1;
                                  
        // 中周期价格趋势
        double longPriceChange = (klineLong.get(0).getClose().doubleValue() / 
                                 klineLong.get(9).getClose().doubleValue()) - 1;
        
        // 短周期成交量趋势
        double shortVolumeChange = (klineShort.get(0).getVolume() / 
                                   klineShort.get(9).getVolume()) - 1;
                                   
        // 中周期成交量趋势
        double longVolumeChange = (klineLong.get(0).getVolume() / 
                                  klineLong.get(9).getVolume()) - 1;
        
        // 检测背离
        boolean shortDivergence = (isLong && shortPriceChange > 0 && shortVolumeChange < 0) || 
                                (!isLong && shortPriceChange < 0 && shortVolumeChange < 0);
                                
        boolean longDivergence = (isLong && longPriceChange > 0 && longVolumeChange < 0) || 
                               (!isLong && longPriceChange < 0 && longVolumeChange < 0);
        
        // 计算背离强度
        if (shortDivergence && longDivergence) {
            return 1.0; // 双周期背离，强信号
        } else if (shortDivergence || longDivergence) {
            return 0.6; // 单周期背离
        }
        
        return 0.0;
    }

    /**
     * 计算ADX指标 (Average Directional Index)
     * 
     * @param klines K线数据
     * @param period ADX计算周期，通常为14
     * @return 包含ADX值和方向的结果对象
     */
    private ADXResult calculateADX(List<KLineEntity> klines, int period) {
        if (klines.size() < period + 1) {
            return new ADXResult(0, 0, 0, "数据不足");
        }

        // 计算+DI和-DI
        double[] plusDM = new double[klines.size() - 1];
        double[] minusDM = new double[klines.size() - 1];
        double[] trueRange = new double[klines.size() - 1];

        // 计算方向变动和真实波幅
        for (int i = 0; i < klines.size() - 1; i++) {
            KLineEntity current = klines.get(i);
            KLineEntity prev = klines.get(i + 1);

            double highDiff = current.getHigh().doubleValue() - prev.getHigh().doubleValue();
            double lowDiff = prev.getLow().doubleValue() - current.getLow().doubleValue();

            // +DM: 如果今日最高价 > 昨日最高价，且差值大于(昨日最低价 - 今日最低价)，则为今日最高价 - 昨日最高价，否则为0
            plusDM[i] = (highDiff > 0 && highDiff > lowDiff) ? highDiff : 0;
            
            // -DM: 如果昨日最低价 > 今日最低价，且差值大于(今日最高价 - 昨日最高价)，则为昨日最低价 - 今日最低价，否则为0
            minusDM[i] = (lowDiff > 0 && lowDiff > highDiff) ? lowDiff : 0;

            // 计算真实波幅TR
            double tr1 = current.getHigh().doubleValue() - current.getLow().doubleValue();
            double tr2 = Math.abs(current.getHigh().doubleValue() - prev.getClose().doubleValue());
            double tr3 = Math.abs(current.getLow().doubleValue() - prev.getClose().doubleValue());
            trueRange[i] = Math.max(Math.max(tr1, tr2), tr3);
        }

        // 计算平滑后的+DM、-DM和TR
        double smoothedPlusDM = 0;
        double smoothedMinusDM = 0;
        double smoothedTR = 0;

        // 初始平滑值为前period个值的和
        for (int i = 0; i < period; i++) {
            smoothedPlusDM += plusDM[i];
            smoothedMinusDM += minusDM[i];
            smoothedTR += trueRange[i];
        }

        // 计算第一个+DI和-DI
        double plusDI = 100 * (smoothedPlusDM / smoothedTR);
        double minusDI = 100 * (smoothedMinusDM / smoothedTR);

        // 计算方向指数DX
        double dx = 100 * (Math.abs(plusDI - minusDI) / (plusDI + minusDI));

        // 计算ADX (第一个值为DX)
        double adx = dx;

        // 计算后续的平滑值和ADX
        for (int i = period; i < klines.size() - 1; i++) {
            // 平滑+DM、-DM和TR
            smoothedPlusDM = smoothedPlusDM - (smoothedPlusDM / period) + plusDM[i];
            smoothedMinusDM = smoothedMinusDM - (smoothedMinusDM / period) + minusDM[i];
            smoothedTR = smoothedTR - (smoothedTR / period) + trueRange[i];

            // 计算+DI和-DI
            plusDI = 100 * (smoothedPlusDM / smoothedTR);
            minusDI = 100 * (smoothedMinusDM / smoothedTR);

            // 计算DX
            dx = 100 * (Math.abs(plusDI - minusDI) / (plusDI + minusDI));

            // 平滑ADX
            adx = ((period - 1) * adx + dx) / period;
        }

        // 判断趋势方向和强度
        String trend = "无趋势";
        if (adx >= ADX_STRONG_TREND_THRESHOLD) {
            trend = plusDI > minusDI ? "强多头趋势" : "强空头趋势";
        } else if (adx >= ADX_TREND_THRESHOLD) {
            trend = plusDI > minusDI ? "多头趋势" : "空头趋势";
        } else if (adx >= 15) {
            trend = plusDI > minusDI ? "弱多头趋势" : "弱空头趋势";
        }

        return new ADXResult(adx, plusDI, minusDI, trend);
    }

    /**
     * 多时间周期K线数据容器
     */
    @Data
    private static class KLineData {
        private final List<KLineEntity> mainKlines;   // 主时间周期（30分钟）
        private final List<KLineEntity> shortKlines;  // 短时间周期（5分钟）
        private final List<KLineEntity> longKlines;   // 长时间周期（4小时）

        public KLineData(List<KLineEntity> mainKlines, List<KLineEntity> shortKlines, List<KLineEntity> longKlines) {
            this.mainKlines = mainKlines != null ? mainKlines : new ArrayList<>();
            this.shortKlines = shortKlines != null ? shortKlines : new ArrayList<>();
            this.longKlines = longKlines != null ? longKlines : new ArrayList<>();
        }

        /**
         * 检查是否有效（所有时间周期都有数据）
         */
        public boolean isValid() {
            return !mainKlines.isEmpty() && !shortKlines.isEmpty() && !longKlines.isEmpty();
        }

        /**
         * 检查是否有足够的最小数据量
         */
        public boolean hasMinimumData(int minSize) {
            return mainKlines.size() >= minSize &&
                   shortKlines.size() >= minSize &&
                   longKlines.size() >= minSize;
        }
    }

    /**
     * ADX计算结果类
     */
    @Data
    private static class ADXResult {
        private final double adx;      // ADX值
        private final double plusDI;   // +DI值
        private final double minusDI;  // -DI值
        private final String trend;    // 趋势描述

        public ADXResult(double adx, double plusDI, double minusDI, String trend) {
            this.adx = adx;
            this.plusDI = plusDI;
            this.minusDI = minusDI;
            this.trend = trend;
        }

        /**
         * 判断是否存在趋势
         */
        public boolean hasTrend() {
            return adx >= ADX_TREND_THRESHOLD;
        }

        /**
         * 判断是否存在强趋势
         */
        public boolean hasStrongTrend() {
            return adx >= ADX_STRONG_TREND_THRESHOLD;
        }

        /**
         * 判断是否为多头趋势
         */
        public boolean isBullishTrend() {
            return plusDI > minusDI;
        }

        /**
         * 获取趋势强度 (0-1)
         */
        public double getTrendStrength() {
            return Math.min(adx / 50.0, 1.0);
        }

        /**
         * 获取趋势方向强度 (-1到1，负值表示空头，正值表示多头)
         */
        public double getDirectionalStrength() {
            double diDiff = plusDI - minusDI;
            double diSum = plusDI + minusDI;
            return diSum > 0 ? diDiff / diSum : 0;
        }
    }
}
